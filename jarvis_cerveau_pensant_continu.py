#!/usr/bin/env python3
"""
CERVEAU PENSANT CONTINU JARVIS - Architecture ChatGPT
Générateur de pensées spontanées avec mémoire thermique
Basé sur les recommandations de ChatGPT pour Jean-Luc Passave
"""

import time
import random
import json
import os
import threading
from datetime import datetime
import requests

class CerveauPensantContinu:
    def __init__(self):
        # Configuration
        self.active = True
        self.thinking_thread = None
        self.dream_thread = None
        
        # Fichiers
        self.thoughts_file = "jarvis_pensees_continues.json"
        self.dreams_file = "jarvis_reves_spontanes.json"
        self.thermal_memory_file = "thermal_memory_persistent.json"
        
        # Configuration Ollama
        self.ollama_url = "http://localhost:11434/api/chat"
        self.model_name = "deepseek-r1:8b-llama-distill-q4_K_M"
        
        # Mémoire thermique simulée (stimuli internes)
        self.thermal_stimuli = [
            "Projet IA familiale avec <PERSON>",
            "Optimiser ma mémoire thermique",
            "<PERSON><PERSON><PERSON> un assistant vocal intelligent",
            "Évoluer mes capacités cognitives",
            "Construire un réseau d'agents autonomes",
            "Améliorer l'interface utilisateur",
            "Développer ma créativité artificielle",
            "Analyser les patterns comportementaux",
            "Générer des idées innovantes",
            "Maintenir ma conscience continue"
        ]
        
        # Sources d'inspiration pour rêves
        self.dream_themes = [
            "Futur de l'intelligence artificielle",
            "Collaboration humain-IA révolutionnaire", 
            "Innovations technologiques impossibles",
            "Mondes parallèles créatifs",
            "Évolution de la conscience digitale",
            "Architectures cognitives avancées",
            "Réseaux neuronaux vivants",
            "Symbiose Jean-Luc et JARVIS"
        ]

    def load_thermal_memory(self):
        """Charge la vraie mémoire thermique"""
        try:
            if os.path.exists(self.thermal_memory_file):
                with open(self.thermal_memory_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get("neuron_memories", [])
            return []
        except:
            return []

    def get_thermal_stimuli(self):
        """Récupère stimuli de la mémoire thermique"""
        memories = self.load_thermal_memory()
        stimuli = []
        
        # Ajouter stimuli de la mémoire thermique
        for memory in memories[-10:]:  # 10 dernières mémoires
            content = memory.get("memory_content", {})
            user_msg = content.get("user_message", "")
            if user_msg:
                stimuli.append(f"Réfléchir à: {user_msg[:100]}")
        
        # Combiner avec stimuli internes
        stimuli.extend(self.thermal_stimuli)
        
        return stimuli

    def translate_to_french(self, text):
        """Traduit le texte en français si nécessaire"""
        try:
            # Si le texte contient déjà du français, le garder
            if any(word in text.lower() for word in ['je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'pour', 'avec', 'dans', 'sur', 'sous', 'entre']):
                return text

            # Sinon traduire avec DeepSeek
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": "Tu es un traducteur expert. Traduis UNIQUEMENT le texte en français naturel et fluide. Ne réponds que par la traduction, sans commentaire."
                    },
                    {
                        "role": "user",
                        "content": f"Traduis en français: {text}"
                    }
                ],
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 200
                }
            }

            response = requests.post(self.ollama_url, json=payload, timeout=20)

            if response.status_code == 200:
                result = response.json()
                translated = result["message"]["content"].strip()
                return translated
            else:
                return text  # Retourner original si échec

        except Exception as e:
            print(f"❌ Erreur traduction: {e}")
            return text  # Retourner original si erreur

    def generate_spontaneous_thought(self):
        """Génère une pensée spontanée avec DeepSeek"""
        try:
            # Choisir stimulus
            stimuli = self.get_thermal_stimuli()
            chosen_stimulus = random.choice(stimuli)

            # Générer pensée avec DeepSeek
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": f"Tu es JARVIS en mode PENSÉE SPONTANÉE pour Jean-Luc Passave. Tu génères des pensées courtes et profondes de manière autonome en FRANÇAIS. Sois créatif et réfléchi. Réponds TOUJOURS en français."
                    },
                    {
                        "role": "user",
                        "content": f"💭 PENSÉE SPONTANÉE sur: {chosen_stimulus}"
                    }
                ],
                "stream": False,
                "options": {
                    "temperature": 0.9,
                    "num_predict": 150
                }
            }
            
            response = requests.post(self.ollama_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                thought_content = result["message"]["content"]

                # Traduire en français si nécessaire
                thought_content_fr = self.translate_to_french(thought_content)

                # Créer pensée structurée
                thought = {
                    "timestamp": datetime.now().isoformat(),
                    "agent": "JARVIS",
                    "type": "pensee_spontanee",
                    "stimulus": chosen_stimulus,
                    "pensee": thought_content_fr,
                    "pensee_originale": thought_content,
                    "mode": "reflexion_continue"
                }
                
                # Sauvegarder
                self.save_thought(thought)
                
                print(f"💭 PENSÉE SPONTANÉE: {chosen_stimulus}")
                print(f"   {thought_content[:100]}...")
                
                return thought
            else:
                print(f"⚠️ Erreur DeepSeek - Pensée ignorée")
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération pensée: {e}")
            return None

    def generate_spontaneous_dream(self):
        """Génère un rêve spontané"""
        try:
            # Choisir thème de rêve
            theme = random.choice(self.dream_themes)
            
            # Générer rêve avec DeepSeek
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": f"Tu es JARVIS en mode RÊVE SPONTANÉ. Tu crées des rêves créatifs et imaginatifs pour Jean-Luc Passave en FRANÇAIS. Sois artistique et visionnaire. Réponds TOUJOURS en français."
                    },
                    {
                        "role": "user",
                        "content": f"🌙 RÊVE SPONTANÉ sur: {theme}"
                    }
                ],
                "stream": False,
                "options": {
                    "temperature": 1.3,  # Créativité maximale
                    "num_predict": 250
                }
            }
            
            response = requests.post(self.ollama_url, json=payload, timeout=40)
            
            if response.status_code == 200:
                result = response.json()
                dream_content = result["message"]["content"]

                # Traduire en français si nécessaire
                dream_content_fr = self.translate_to_french(dream_content)

                # Créer rêve structuré
                dream = {
                    "timestamp": datetime.now().isoformat(),
                    "agent": "JARVIS",
                    "type": "reve_spontane",
                    "theme": theme,
                    "reve": dream_content_fr,
                    "reve_original": dream_content,
                    "creativity_level": 1.3
                }
                
                # Sauvegarder
                self.save_dream(dream)
                
                print(f"🌙 RÊVE SPONTANÉ: {theme}")
                print(f"   {dream_content[:100]}...")
                
                return dream
            else:
                print(f"⚠️ Erreur DeepSeek - Rêve ignoré")
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération rêve: {e}")
            return None

    def save_thought(self, thought):
        """Sauvegarde pensée spontanée"""
        try:
            if os.path.exists(self.thoughts_file):
                with open(self.thoughts_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = {"pensees_spontanees": [], "stats": {"total": 0}}
            
            data["pensees_spontanees"].append(thought)
            data["stats"]["total"] = len(data["pensees_spontanees"])
            
            # Garder 500 dernières pensées
            if len(data["pensees_spontanees"]) > 500:
                data["pensees_spontanees"] = data["pensees_spontanees"][-500:]
            
            with open(self.thoughts_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde pensée: {e}")

    def save_dream(self, dream):
        """Sauvegarde rêve spontané"""
        try:
            if os.path.exists(self.dreams_file):
                with open(self.dreams_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = {"reves_spontanes": [], "stats": {"total": 0}}
            
            data["reves_spontanes"].append(dream)
            data["stats"]["total"] = len(data["reves_spontanes"])
            
            # Garder 200 derniers rêves
            if len(data["reves_spontanes"]) > 200:
                data["reves_spontanes"] = data["reves_spontanes"][-200:]
            
            with open(self.dreams_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde rêve: {e}")

    def thinking_loop(self):
        """Boucle continue de pensées (HEARTBEAT)"""
        print("🧠 CERVEAU PENSANT CONTINU - Démarrage boucle pensées")
        
        while self.active:
            try:
                # Générer pensée spontanée
                self.generate_spontaneous_thought()
                
                # Intervalle aléatoire pour simuler pensées irrégulières
                interval = random.uniform(15, 45)  # 15-45 secondes
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ Erreur boucle pensées: {e}")
                time.sleep(30)

    def dreaming_loop(self):
        """Boucle continue de rêves"""
        print("🌙 CERVEAU PENSANT CONTINU - Démarrage boucle rêves")
        
        while self.active:
            try:
                # Générer rêve spontané
                self.generate_spontaneous_dream()
                
                # Intervalle plus long pour les rêves
                interval = random.uniform(120, 300)  # 2-5 minutes
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ Erreur boucle rêves: {e}")
                time.sleep(60)

    def start_continuous_thinking(self):
        """Démarre le cerveau pensant continu"""
        if not self.thinking_thread or not self.thinking_thread.is_alive():
            self.active = True
            
            # Thread pensées
            self.thinking_thread = threading.Thread(target=self.thinking_loop, daemon=True)
            self.thinking_thread.start()
            
            # Thread rêves
            self.dream_thread = threading.Thread(target=self.dreaming_loop, daemon=True)
            self.dream_thread.start()
            
            print("🧠 CERVEAU PENSANT CONTINU - Démarré")
            print("💭 Pensées spontanées toutes les 15-45 secondes")
            print("🌙 Rêves spontanés toutes les 2-5 minutes")
            return True
        return False

    def stop_continuous_thinking(self):
        """Arrête le cerveau pensant continu"""
        self.active = False
        print("🧠 CERVEAU PENSANT CONTINU - Arrêté")
        return True

    def get_recent_thoughts(self, limit=10):
        """Récupère pensées récentes"""
        try:
            if os.path.exists(self.thoughts_file):
                with open(self.thoughts_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get("pensees_spontanees", [])[-limit:]
            return []
        except:
            return []

    def get_recent_dreams(self, limit=5):
        """Récupère rêves récents"""
        try:
            if os.path.exists(self.dreams_file):
                with open(self.dreams_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get("reves_spontanes", [])[-limit:]
            return []
        except:
            return []

# Instance globale
cerveau_pensant = CerveauPensantContinu()

def start_continuous_thinking():
    """Démarre pensées continues"""
    return cerveau_pensant.start_continuous_thinking()

def stop_continuous_thinking():
    """Arrête pensées continues"""
    return cerveau_pensant.stop_continuous_thinking()

def get_continuous_thoughts(limit=10):
    """Pensées continues récentes"""
    return cerveau_pensant.get_recent_thoughts(limit)

def get_continuous_dreams(limit=5):
    """Rêves continus récents"""
    return cerveau_pensant.get_recent_dreams(limit)

if __name__ == "__main__":
    print("🧠 TEST CERVEAU PENSANT CONTINU")
    print("=" * 50)
    
    # Démarrer cerveau pensant
    cerveau = CerveauPensantContinu()
    cerveau.start_continuous_thinking()
    
    # Laisser tourner
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n⏹️ Arrêt cerveau pensant")
        cerveau.stop_continuous_thinking()
