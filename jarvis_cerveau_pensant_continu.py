#!/usr/bin/env python3
"""
JARVIS - Cerveau Pensant Continu
Système de pensée autonome continue SANS OLLAMA
Auteur: <PERSON><PERSON><PERSON>
Date: 2025-06-21
"""

import time
import random
import json
import os
import threading
from datetime import datetime
import requests

class CerveauPensantContinu:
    """Cerveau pensant en continu avec mémoire thermique"""
    
    def __init__(self):
        self.active = False
        self.pensee_thread = None
        self.pensees_count = 0
        self.reves_count = 0
        
        # Configuration VLLM DIRECT
        self.vllm_url = "http://localhost:8000/v1/chat/completions"
        self.model_name = "deepseek-r1-distill-qwen-8b"
        
        # Fichiers de sauvegarde
        self.pensees_file = "jarvis_pensees_continues.json"
        self.reves_file = "jarvis_reves_spontanes.json"
        
        # Mémoire thermique simple
        self.memoire_chaude = []
        self.memoire_tiede = []
        self.memoire_froide = []
        
        print("🧠 CERVEAU PENSANT CONTINU initialisé")
    
    def demarrer_pensee_continue(self):
        """Démarre la pensée continue"""
        self.active = True
        self.pensee_thread = threading.Thread(target=self.boucle_pensee, daemon=False)
        self.pensee_thread.start()
        print("🔄 PENSÉE CONTINUE démarrée")
    
    def boucle_pensee(self):
        """Boucle principale de pensée"""
        while self.active:
            try:
                # Générer pensée spontanée
                pensee = self.generer_pensee_spontanee()
                if pensee:
                    self.sauvegarder_pensee(pensee)
                    self.pensees_count += 1
                    print(f"💭 PENSÉE SPONTANÉE #{self.pensees_count}")
                
                # Intervalle aléatoire
                interval = random.uniform(15, 45)
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ Erreur pensée continue: {e}")
                time.sleep(30)
    
    def generer_pensee_spontanee(self):
        """Génère une pensée spontanée"""
        try:
            # Stimulus basé sur mémoire
            stimulus = self.choisir_stimulus()
            
            prompt = f"""Tu es JARVIS en mode pensée spontanée.

STIMULUS: {stimulus}

Génère une pensée spontanée qui:
1. Part du stimulus donné
2. Développe une réflexion personnelle
3. Propose une idée constructive
4. Reste concise (100-150 mots)

Utilise <think> pour montrer ta réflexion."""
            
            # Essayer VLLM d'abord
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": "Tu es JARVIS avec pensée spontanée continue."},
                    {"role": "user", "content": prompt}
                ],
                "stream": False,
                "temperature": 0.9,
                "max_tokens": 200
            }
            
            response = requests.post(self.vllm_url, json=payload, timeout=20)
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                # Mode dégradé
                return self.pensee_degradee(stimulus)
                
        except Exception as e:
            print(f"❌ Erreur génération pensée: {e}")
            return self.pensee_degradee("réflexion générale")
    
    def pensee_degradee(self, stimulus):
        """Pensée en mode dégradé"""
        pensees_base = [
            f"Je réfléchis à {stimulus} et comment l'optimiser pour Jean-Luc.",
            f"Analysant {stimulus}, je vois des opportunités d'amélioration.",
            f"Ma réflexion sur {stimulus} révèle des connexions intéressantes.",
            f"Explorant {stimulus}, je développe de nouvelles perspectives.",
            f"Considérant {stimulus}, j'identifie des solutions innovantes."
        ]
        return random.choice(pensees_base)
    
    def choisir_stimulus(self):
        """Choisit un stimulus pour la pensée"""
        stimuli_base = [
            "l'optimisation cognitive",
            "l'interface utilisateur",
            "la créativité artificielle",
            "l'efficacité système",
            "l'innovation technologique",
            "l'expérience utilisateur",
            "l'automatisation intelligente",
            "la mémoire thermique",
            "les connexions neuronales",
            "l'évolution adaptative"
        ]
        
        # Utiliser mémoire chaude si disponible
        if self.memoire_chaude:
            return random.choice(self.memoire_chaude + stimuli_base)
        
        return random.choice(stimuli_base)
    
    def sauvegarder_pensee(self, pensee):
        """Sauvegarde une pensée"""
        try:
            # Charger données existantes
            if os.path.exists(self.pensees_file):
                with open(self.pensees_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = {"pensees_spontanees": [], "stats": {"total": 0}}
            
            # Ajouter nouvelle pensée
            nouvelle_pensee = {
                "timestamp": datetime.now().isoformat(),
                "pensee": pensee,
                "stimulus": self.choisir_stimulus(),
                "mode": "spontane"
            }
            
            data["pensees_spontanees"].append(nouvelle_pensee)
            data["stats"]["total"] = len(data["pensees_spontanees"])
            
            # Garder 1000 dernières pensées
            if len(data["pensees_spontanees"]) > 1000:
                data["pensees_spontanees"] = data["pensees_spontanees"][-1000:]
            
            # Sauvegarder
            with open(self.pensees_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Ajouter à mémoire chaude
            self.memoire_chaude.append(pensee[:50])
            if len(self.memoire_chaude) > 10:
                self.memoire_chaude.pop(0)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde pensée: {e}")

def get_continuous_thoughts(limit=10):
    """Récupère pensées continues récentes"""
    try:
        if os.path.exists("jarvis_pensees_continues.json"):
            with open("jarvis_pensees_continues.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            pensees = data.get("pensees_spontanees", [])[-limit:]
            return pensees
        return []
    except:
        return []

def get_continuous_dreams(limit=5):
    """Récupère rêves continus récents"""
    try:
        if os.path.exists("jarvis_reves_spontanes.json"):
            with open("jarvis_reves_spontanes.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            reves = data.get("reves_spontanes", [])[-limit:]
            return reves
        return []
    except:
        return []

if __name__ == "__main__":
    print("🧠 JARVIS CERVEAU PENSANT CONTINU")
    print("=" * 50)
    print("Pensée autonome continue SANS OLLAMA")
    
    cerveau = CerveauPensantContinu()
    cerveau.demarrer_pensee_continue()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n🧠 Arrêt du cerveau pensant")
        cerveau.active = False
