#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 JARVIS - MODULE DE CONFIGURATION
Pour Jean-Luc Passave - 20 juin 2025
Configuration centralisée et sécurisée
"""

import os
import json
from datetime import datetime

# ============================================================================
# CONFIGURATION PRINCIPALE JARVIS
# ============================================================================

# Informations utilisateur
USER_NAME = "Jean-Luc Passave"
USER_ROLE = "Créateur et développeur principal de JARVIS"
USER_EXPERTISE = "Intelligence artificielle, DeepSeek R1, mémoire thermique"

# Configuration serveur DeepSeek R1 8B
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "deepseek-ai/DeepSeek-R1-0528"
SERVER_TIMEOUT = 30

# Configuration interface Gradio
GRADIO_HOST = "localhost"
GRADIO_PORT = 7867
GRADIO_SHARE = False

# Fichiers de données
MEMORY_FILE = "thermal_memory_persistent.json"
BACKUP_DIR = "SAUVEGARDES"
LOG_FILE = "jarvis.log"

# Configuration mémoire thermique
MEMORY_MAX_ENTRIES = 1000
MEMORY_CLEANUP_THRESHOLD = 1200
THERMAL_ZONES = [
    "input_processing",
    "agent1_output", 
    "agent2_output",
    "keyword_indexing",
    "summary",
    "proactive_suggestions"
]

# Configuration agents
AGENTS = {
    "agent1": {
        "name": "JARVIS Principal",
        "role": "Conversation utilisateur",
        "temperature": 0.7,
        "max_tokens": 2000
    },
    "agent2": {
        "name": "JARVIS Thermique", 
        "role": "Analyse mémoire et suggestions proactives",
        "temperature": 0.5,
        "max_tokens": 1000
    }
}

# Configuration sécurité
SECURITY_SETTINGS = {
    "auto_backup": True,
    "backup_frequency": 3600,  # 1 heure
    "max_backup_files": 10,
    "validate_inputs": True,
    "log_level": "INFO"
}

# Configuration interface
INTERFACE_SETTINGS = {
    "theme": "dark",
    "language": "fr",
    "show_thinking": True,
    "auto_scroll": True,
    "max_history_display": 50,
    "thoughts_extraction": True,
    "thoughts_display_format": "html",
    "thoughts_auto_update": True,
    "creativity_mode": True,
    "creativity_auto_generation": True,
    "creativity_frequency_minutes": 10
}

# ============================================================================
# FONCTIONS DE CONFIGURATION
# ============================================================================

def get_config():
    """Retourne la configuration complète"""
    return {
        "user": {
            "name": USER_NAME,
            "role": USER_ROLE,
            "expertise": USER_EXPERTISE
        },
        "server": {
            "url": SERVER_URL,
            "model": MODEL_NAME,
            "timeout": SERVER_TIMEOUT
        },
        "gradio": {
            "host": GRADIO_HOST,
            "port": GRADIO_PORT,
            "share": GRADIO_SHARE
        },
        "files": {
            "memory": MEMORY_FILE,
            "backup_dir": BACKUP_DIR,
            "log": LOG_FILE
        },
        "memory": {
            "max_entries": MEMORY_MAX_ENTRIES,
            "cleanup_threshold": MEMORY_CLEANUP_THRESHOLD,
            "thermal_zones": THERMAL_ZONES
        },
        "agents": AGENTS,
        "security": SECURITY_SETTINGS,
        "interface": INTERFACE_SETTINGS
    }

def validate_config():
    """Valide la configuration et crée les dossiers nécessaires"""
    try:
        # Créer le dossier de sauvegarde
        if not os.path.exists(BACKUP_DIR):
            os.makedirs(BACKUP_DIR)
            print(f"✅ Dossier créé: {BACKUP_DIR}")
        
        # Vérifier les fichiers critiques
        config_status = {
            "backup_dir": os.path.exists(BACKUP_DIR),
            "memory_file_exists": os.path.exists(MEMORY_FILE),
            "server_accessible": False  # À tester séparément
        }
        
        return config_status
        
    except Exception as e:
        print(f"❌ Erreur validation config: {e}")
        return {"error": str(e)}

def save_config_backup():
    """Sauvegarde la configuration actuelle"""
    try:
        config = get_config()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(BACKUP_DIR, f"config_backup_{timestamp}.json")

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print(f"✅ Configuration sauvegardée: {backup_file}")
        return backup_file

    except Exception as e:
        print(f"❌ Erreur sauvegarde config: {e}")
        return None

def save_thoughts_creativity_config(thoughts_enabled=True, creativity_enabled=True, creativity_freq=10):
    """SAUVEGARDE SPÉCIFIQUE POUR PENSÉES ET CRÉATIVITÉ - JEAN-LUC PASSAVE"""
    try:
        config_file = "jarvis_thoughts_creativity_config.json"

        config = {
            "thoughts_system": {
                "enabled": thoughts_enabled,
                "extraction_active": True,
                "display_format": "html_enhanced",
                "auto_update": True,
                "show_in_interface": True,
                "save_to_memory": True
            },
            "creativity_system": {
                "enabled": creativity_enabled,
                "auto_generation": True,
                "frequency_minutes": creativity_freq,
                "proactive_mode": True,
                "save_creations": True,
                "integration_deepseek": True
            },
            "last_updated": datetime.now().isoformat(),
            "user": "Jean-Luc Passave"
        }

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print(f"✅ Configuration pensées/créativité sauvegardée: {config_file}")
        return config_file

    except Exception as e:
        print(f"❌ Erreur sauvegarde pensées/créativité: {e}")
        return None

def load_thoughts_creativity_config():
    """CHARGE LA CONFIGURATION PENSÉES ET CRÉATIVITÉ"""
    try:
        config_file = "jarvis_thoughts_creativity_config.json"

        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Créer la configuration par défaut
            return save_thoughts_creativity_config()

    except Exception as e:
        print(f"❌ Erreur chargement config pensées/créativité: {e}")
        return None

def get_system_info():
    """Informations système pour diagnostic"""
    try:
        import platform
        import psutil
        
        return {
            "platform": platform.system(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": f"{psutil.virtual_memory().total / (1024**3):.1f} GB",
            "memory_available": f"{psutil.virtual_memory().available / (1024**3):.1f} GB",
            "disk_free": f"{psutil.disk_usage('/').free / (1024**3):.1f} GB"
        }
        
    except Exception as e:
        return {"error": f"Impossible d'obtenir les infos système: {e}"}

# ============================================================================
# CONSTANTES POUR LES AUTRES MODULES
# ============================================================================

# Messages système
SYSTEM_MESSAGES = {
    "startup": "🚀 JARVIS - Démarrage du système",
    "shutdown": "🔴 JARVIS - Arrêt du système", 
    "memory_saved": "🧠 Mémoire thermique sauvegardée",
    "error": "❌ Erreur système",
    "success": "✅ Opération réussie"
}

# Couleurs interface
COLORS = {
    "primary": "#2196F3",
    "secondary": "#4CAF50", 
    "warning": "#FF9800",
    "error": "#F44336",
    "success": "#4CAF50",
    "background": "#1a1a1a",
    "text": "#ffffff"
}

# Patterns de recherche
SEARCH_PATTERNS = {
    "user_name": r"jean-luc\s+passave?",
    "technical_terms": r"(jarvis|deepseek|mémoire|thermique|agent)",
    "questions": r"(\?|comment|pourquoi|peux-tu|pourrais-tu)",
    "commands": r"(lance|ouvre|ferme|démarre|arrête)"
}

if __name__ == "__main__":
    print("🔧 JARVIS CONFIG - Test de configuration")
    config = get_config()
    print(f"✅ Configuration chargée pour {config['user']['name']}")
    
    status = validate_config()
    print(f"📊 Statut: {status}")
    
    sys_info = get_system_info()
    print(f"💻 Système: {sys_info}")
