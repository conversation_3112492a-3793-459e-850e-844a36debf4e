#!/usr/bin/env node

const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
    console.log('🚀 Création fenêtre JARVIS Electron Simple');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        },
        title: 'JARVIS M4 - Interface Native',
        icon: path.join(__dirname, 'assets', 'jarvis-icon.png'),
        show: false
    });

    // Interface HTML simple
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>JARVIS M4 Interface</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                margin: 0;
                padding: 20px;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .stats {
                display: flex;
                justify-content: space-around;
                margin: 20px 0;
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
            }
            .buttons {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .btn {
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s;
            }
            .btn:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-2px);
            }
            .chat {
                background: rgba(0,0,0,0.3);
                border-radius: 10px;
                padding: 20px;
                height: 300px;
                overflow-y: auto;
                margin: 20px 0;
            }
            .message {
                margin: 10px 0;
                padding: 10px;
                border-radius: 5px;
                background: rgba(255,255,255,0.1);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 JARVIS M4 - Interface Native</h1>
                <p>Intelligence Artificielle avec Mémoire Thermique</p>
            </div>
            
            <div class="stats">
                <div>
                    <h3>🔥 Neurones</h3>
                    <p id="neurons">89.00B</p>
                </div>
                <div>
                    <h3>📊 QI Évolutif</h3>
                    <p id="qi">164</p>
                </div>
                <div>
                    <h3>🌡️ Statut</h3>
                    <p id="status">Connecté</p>
                </div>
            </div>
            
            <div class="buttons">
                <button class="btn" onclick="openDashboard()">🏠 Dashboard Principal</button>
                <button class="btn" onclick="openCommunication()">💬 Communication</button>
                <button class="btn" onclick="openLTXVideo()">🎬 LTX Vidéo</button>
                <button class="btn" onclick="openStableVideo()">📹 Stable Video</button>
                <button class="btn" onclick="openMultimedia()">🎨 Générateur Multimédia</button>
                <button class="btn" onclick="openCognitive()">🧠 Analyse Cognitive</button>
                <button class="btn" onclick="openMemory()">🌡️ Mémoire Thermique</button>
                <button class="btn" onclick="openAPI()">🚀 API JARVIS V2</button>
            </div>
            
            <div class="chat" id="chat">
                <div class="message">🤖 JARVIS M4 connecté ! Interface native prête.</div>
                <div class="message">🧠 Système cognitif: LTX Vidéo + Stable Video connectés</div>
                <div class="message">🌡️ Mémoire thermique: Active avec indexation automatique</div>
            </div>
        </div>
        
        <script>
            const { shell } = require('electron');
            
            function openDashboard() {
                shell.openExternal('http://localhost:7867');
            }
            
            function openCommunication() {
                shell.openExternal('http://localhost:7866');
            }
            
            function openLTXVideo() {
                shell.openExternal('http://localhost:7863');
            }
            
            function openStableVideo() {
                shell.openExternal('http://localhost:7861');
            }
            
            function openMultimedia() {
                shell.openExternal('http://localhost:7867/multimedia');
            }
            
            function openCognitive() {
                shell.openExternal('http://localhost:7867/cognitive');
            }
            
            function openMemory() {
                shell.openExternal('http://localhost:7874');
            }
            
            function openAPI() {
                shell.openExternal('http://localhost:8000/docs');
            }
            
            // Vérifier connexions
            setInterval(() => {
                fetch('http://localhost:8000/health')
                    .then(() => {
                        document.getElementById('status').textContent = 'Connecté';
                    })
                    .catch(() => {
                        document.getElementById('status').textContent = 'Déconnecté';
                    });
            }, 5000);
        </script>
    </body>
    </html>
    `;

    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent));
    
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('✅ Interface JARVIS Simple affichée');
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

console.log('🚀 JARVIS Electron Simple - Démarrage');
