#!/usr/bin/env python3
"""
INTERFACE RÊVES-CRÉATIVITÉ JARVIS
Visualisation complète de tous les rêves, créations et pensées créatives de JARVIS
RÊVES = CRÉATIVITÉ = MÊME CHOSE
Pour Jean-Luc Passave
"""

import gradio as gr
import json
import os
from datetime import datetime
import time

def load_all_creative_content():
    """Charge TOUT le contenu créatif de JARVIS (RÊVES = CRÉATIVITÉ)"""
    all_creative = []

    try:
        # 1. Rêves créatifs du cerveau conscient 24h
        if os.path.exists('jarvis_reves_creatifs.json'):
            with open('jarvis_reves_creatifs.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            reves_creatifs = data.get('reves_creatifs', [])

            for reve in reves_creatifs:
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "🌙 Rêve Créatif",
                    "source": "Cerveau Conscient 24h",
                    "sujet": reve.get('sujet', ''),
                    "contenu": reve.get('reve', ''),
                    "creativite": reve.get('creativite_level', 'Standard'),
                    "category": "creation"
                })

        # 2. Pensées créatives (pensées avec créativité élevée)
        if os.path.exists('jarvis_pensees_eveil.json'):
            with open('jarvis_pensees_eveil.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            pensees_eveil = data.get('pensees_eveil', [])

            for pensee in pensees_eveil:
                # Filtrer les pensées créatives
                contenu = pensee.get('pensee', '').lower()
                if any(word in contenu for word in ['créer', 'innover', 'imaginer', 'concevoir', 'inventer', 'créatif', 'innovation', 'idée']):
                    all_creative.append({
                        "timestamp": pensee.get('timestamp', ''),
                        "type": "💡 Pensée Créative",
                        "source": "Cerveau Conscient Éveil",
                        "sujet": pensee.get('sujet', ''),
                        "contenu": pensee.get('pensee', ''),
                        "creativite": "Créative",
                        "category": "pensee_creative"
                    })
        
        # 3. Rêves spontanés du cerveau pensant continu
        if os.path.exists('jarvis_reves_spontanes.json'):
            with open('jarvis_reves_spontanes.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            reves_spontanes = data.get('reves_spontanes', [])

            for reve in reves_spontanes:
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "✨ Rêve Spontané",
                    "source": "Cerveau Pensant Continu",
                    "sujet": reve.get('theme', ''),
                    "contenu": reve.get('reve', ''),
                    "creativite": reve.get('creativity_level', 1.3),
                    "category": "reve_spontane"
                })

        # 4. Rêves immortels du cerveau anti-déconnexion
        if os.path.exists('jarvis_reves_immortels.json'):
            with open('jarvis_reves_immortels.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            reves_immortels = data.get('reves_immortels', [])

            for reve in reves_immortels:
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "🛡️ Rêve Immortel",
                    "source": "Cerveau Anti-Déconnexion",
                    "sujet": reve.get('theme', ''),
                    "contenu": reve.get('reve', ''),
                    "creativite": "Immortel",
                    "category": "reve_immortel"
                })

        # 5. Auto-questions créatives (questions sur créativité)
        if os.path.exists('jarvis_auto_questions.json'):
            with open('jarvis_auto_questions.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            auto_questions = data.get('auto_questions', [])

            for qa in auto_questions:
                question = qa.get('question', '').lower()
                if any(word in question for word in ['créatif', 'créativité', 'innover', 'idée', 'concevoir']):
                    all_creative.append({
                        "timestamp": qa.get('timestamp', ''),
                        "type": "🤔 Question Créative",
                        "source": "Auto-Questionnement",
                        "sujet": qa.get('question', ''),
                        "contenu": qa.get('reponse', ''),
                        "creativite": "Auto-Evolution",
                        "category": "question_creative"
                    })

        # Trier par timestamp (plus récent en premier)
        all_creative.sort(key=lambda x: x['timestamp'], reverse=True)

        return all_creative
        
    except Exception as e:
        print(f"❌ Erreur chargement rêves: {e}")
        return []

def format_creative_content_for_display():
    """Formate le contenu créatif pour l'affichage Gradio"""
    creative_content = load_all_creative_content()

    if not creative_content:
        return [{"role": "assistant", "content": "🌙 Aucun contenu créatif disponible - JARVIS dort profondément..."}]
    
    messages = []
    
    for dream in creative_content:
        # En-tête du rêve
        timestamp = dream['timestamp']
        if timestamp:
            time_str = timestamp[11:19] if len(timestamp) > 19 else timestamp
        else:
            time_str = "??:??:??"
        
        header = f"{dream['type']} | {time_str} | {dream['source']}"
        messages.append({"role": "user", "content": header})
        
        # Sujet du rêve
        if dream['sujet']:
            messages.append({"role": "user", "content": f"🎯 SUJET: {dream['sujet']}"})
        
        # Contenu du rêve
        contenu = dream['contenu']
        if contenu:
            # Limiter la longueur pour l'affichage
            if len(contenu) > 1000:
                contenu = contenu[:1000] + "... [RÊVE TRONQUÉ - Voir fichier complet]"
            
            messages.append({"role": "assistant", "content": f"💭 {contenu}"})
        
        # Niveau de créativité
        creativite = dream['creativite']
        if creativite:
            messages.append({"role": "assistant", "content": f"🎨 Créativité: {creativite}"})
        
        # Séparateur
        messages.append({"role": "assistant", "content": "─" * 50})
    
    return messages

def get_dreams_stats():
    """Statistiques des rêves créatifs - FONCTION CORRIGÉE"""
    creative_content = load_all_creative_content()

    if not creative_content:
        return "📊 Aucun contenu créatif enregistré"

    # Compter par type
    stats = {}
    for item in creative_content:
        item_type = item['type']
        if item_type in stats:
            stats[item_type] += 1
        else:
            stats[item_type] = 1

    # Compter par source
    sources = {}
    for item in creative_content:
        source = item['source']
        if source in sources:
            sources[source] += 1
        else:
            sources[source] = 1

    # Dernier contenu créatif
    last_item = creative_content[0] if creative_content else None
    last_time = last_item['timestamp'][11:19] if last_item and last_item['timestamp'] else "Jamais"

    stats_text = f"""
📊 **STATISTIQUES CRÉATIVITÉ JARVIS**

🎨 **Total créations**: {len(creative_content)}

📈 **Par type**:
{chr(10).join([f"   • {t}: {c}" for t, c in stats.items()])}

🧠 **Par source**:
{chr(10).join([f"   • {s}: {c}" for s, c in sources.items()])}

🕐 **Dernier rêve**: {last_time}
    """

    return stats_text

def export_all_dreams():
    """Exporte tous les rêves - FONCTION CORRIGÉE"""
    try:
        creative_content = load_all_creative_content()

        if not creative_content:
            return "❌ Aucun contenu créatif à exporter"

        # Créer fichier d'export
        export_filename = f"jarvis_creativite_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_creative": len(creative_content),
            "creative_content": creative_content
        }

        with open(export_filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        return f"✅ Rêves exportés: {export_filename}"

    except Exception as e:
        return f"❌ Erreur export: {e}"

def clear_all_dreams():
    """Efface tous les rêves (avec confirmation) - FONCTION CORRIGÉE"""
    try:
        # Sauvegarder avant effacement
        backup_filename = f"jarvis_reves_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        dreams = load_all_creative_content()  # CORRIGÉ

        if dreams:
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump({"backup_dreams": dreams}, f, ensure_ascii=False, indent=2)
        
        # Effacer les fichiers
        files_to_clear = [
            'jarvis_reves_creatifs.json',
            'jarvis_reves_spontanes.json', 
            'jarvis_reves_immortels.json'
        ]
        
        cleared_count = 0
        for filename in files_to_clear:
            if os.path.exists(filename):
                # Vider le fichier mais garder la structure
                if 'creatifs' in filename:
                    empty_data = {"reves_creatifs": [], "stats": {"total": 0}}
                elif 'spontanes' in filename:
                    empty_data = {"reves_spontanes": [], "stats": {"total": 0}}
                elif 'immortels' in filename:
                    empty_data = {"reves_immortels": [], "stats": {"total": 0}}
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(empty_data, f, ensure_ascii=False, indent=2)
                
                cleared_count += 1
        
        return f"✅ {cleared_count} fichiers de rêves effacés\n💾 Sauvegarde: {backup_filename}"
        
    except Exception as e:
        return f"❌ Erreur effacement: {e}"

# Interface Gradio
def create_dreams_interface():
    """Crée l'interface des rêves"""
    
    with gr.Blocks(
        title="🌙 JARVIS - Rêves Créatifs",
        theme=gr.themes.Soft(),
        css="""
        .dreams-chatbot {
            font-size: 16px !important;
            line-height: 1.6 !important;
        }
        .dreams-chatbot .message {
            padding: 15px !important;
            margin: 10px 0 !important;
            border-radius: 10px !important;
        }
        .dreams-chatbot .user {
            background: linear-gradient(135deg, #E8F5E8, #C8E6C9) !important;
            border-left: 4px solid #4CAF50 !important;
        }
        .dreams-chatbot .assistant {
            background: linear-gradient(135deg, #F3E5F5, #E1BEE7) !important;
            border-left: 4px solid #9C27B0 !important;
        }
        """
    ) as dreams_interface:
        
        gr.HTML("<h1>🌙 JARVIS - Interface Rêves Créatifs</h1>")
        gr.HTML("<p>Visualisation complète de tous les rêves et créations de JARVIS</p>")
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>📊 Statistiques</h3>")
                stats_display = gr.Markdown(
                    value=get_dreams_stats(),
                    label="Statistiques des rêves"
                )
                
                gr.HTML("<h3>🎛️ Contrôles</h3>")
                refresh_btn = gr.Button("🔄 Actualiser", variant="primary")
                export_btn = gr.Button("📥 Exporter Rêves", variant="secondary")
                clear_btn = gr.Button("🗑️ Effacer Rêves", variant="secondary")
                
                # Zone de statut
                status_display = gr.Textbox(
                    label="Statut",
                    value="✅ Interface rêves prête",
                    interactive=False
                )
            
            with gr.Column(scale=3):
                gr.HTML("<h3>🌙 Flux de Rêves JARVIS</h3>")
                dreams_display = gr.Chatbot(
                    value=format_creative_content_for_display(),  # CORRIGÉ
                    height=600,
                    label="🌙 Tous les Rêves de JARVIS - Mise à jour automatique",
                    type="messages",
                    elem_classes=["dreams-chatbot"]
                )
        
        # Fonctions de callback
        def refresh_dreams():
            """Actualise les rêves et statistiques"""
            return format_creative_content_for_display(), get_dreams_stats(), "🔄 Rêves actualisés"  # CORRIGÉ
        
        def export_dreams():
            """Exporte les rêves"""
            result = export_all_dreams()
            return format_creative_content_for_display(), get_dreams_stats(), result  # CORRIGÉ

        def clear_dreams():
            """Efface les rêves"""
            result = clear_all_dreams()
            return format_creative_content_for_display(), get_dreams_stats(), result  # CORRIGÉ
        
        # Connecter les boutons
        refresh_btn.click(
            fn=refresh_dreams,
            outputs=[dreams_display, stats_display, status_display]
        )
        
        export_btn.click(
            fn=export_dreams,
            outputs=[dreams_display, stats_display, status_display]
        )
        
        clear_btn.click(
            fn=clear_dreams,
            outputs=[dreams_display, stats_display, status_display]
        )
        
        # Mise à jour automatique au chargement
        dreams_interface.load(
            fn=refresh_dreams,
            outputs=[dreams_display, stats_display, status_display]
        )
        
        # Auto-refresh JavaScript
        gr.HTML("""
        <script>
        function autoRefreshDreams() {
            setInterval(function() {
                try {
                    const refreshBtn = document.querySelector('button:contains("🔄")');
                    if (refreshBtn) {
                        refreshBtn.click();
                        console.log('🌙 Rêves JARVIS mis à jour automatiquement');
                    }
                } catch (e) {
                    console.log('Erreur mise à jour auto rêves:', e);
                }
            }, 30000); // 30 secondes
        }
        
        setTimeout(autoRefreshDreams, 3000);
        </script>
        <div style="display:none;">Auto-refresh rêves JARVIS activé</div>
        """, visible=False)
    
    return dreams_interface

if __name__ == "__main__":
    print("🌙 INTERFACE RÊVES CRÉATIFS JARVIS")
    print("=" * 50)
    
    # Créer et lancer l'interface
    interface = create_dreams_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7891,  # Port dédié aux rêves
        share=False,
        show_error=True
    )
