#!/usr/bin/env python3
"""
🤖 TEST SIMPLE IA LOCALE AUTONOME
================================

Test rapide de l'IA locale pour validation du concept
Auteur: <PERSON><PERSON><PERSON> + Claude
Date: 2025-06-21
"""

import requests
import json

def test_ollama_direct():
    """Test direct d'Ollama"""
    print("🧪 Test direct d'Ollama...")
    
    try:
        # Test simple avec prompt court
        payload = {
            "model": "mistral",
            "prompt": "Bonjour ! Réponds en une phrase courte.",
            "stream": False,
            "options": {
                "num_predict": 50,  # Limite à 50 tokens
                "temperature": 0.7
            }
        }
        
        print("🔄 Envoi requête à Ollama...")
        response = requests.post(
            "http://localhost:11434/api/generate", 
            json=payload, 
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            reponse = result.get('response', '').strip()
            print(f"✅ Réponse IA locale: {reponse}")
            return True
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_modeles_disponibles():
    """Lister les modèles disponibles"""
    print("📦 Modèles disponibles:")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            for model in models:
                print(f"  • {model['name']} ({model.get('size', 'taille inconnue')})")
            return models
        else:
            print("❌ Impossible de récupérer la liste des modèles")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def conversation_simple():
    """Conversation simple avec l'IA locale"""
    print("\n💬 CONVERSATION SIMPLE AVEC IA LOCALE")
    print("Tapez 'exit' pour quitter")
    print("-" * 40)
    
    while True:
        user_input = input("\n👨‍💼 Vous: ").strip()
        
        if user_input.lower() in ['exit', 'quit']:
            print("👋 Au revoir !")
            break
            
        if not user_input:
            continue
            
        try:
            payload = {
                "model": "mistral",
                "prompt": f"Réponds brièvement à: {user_input}",
                "stream": False,
                "options": {
                    "num_predict": 100,
                    "temperature": 0.7
                }
            }
            
            print("🤖 JARVIS réfléchit...")
            response = requests.post(
                "http://localhost:11434/api/generate", 
                json=payload, 
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                reponse = result.get('response', '').strip()
                print(f"🤖 JARVIS: {reponse}")
            else:
                print(f"❌ Erreur: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("🚀 TEST SIMPLE IA LOCALE AUTONOME")
    print("=" * 40)
    
    # Test 1: Vérifier Ollama
    print("1️⃣ Vérification d'Ollama...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        if response.status_code == 200:
            print("✅ Ollama actif")
        else:
            print("❌ Ollama non accessible")
            return
    except:
        print("❌ Ollama non démarré")
        print("💡 Démarrez: ollama serve")
        return
    
    # Test 2: Lister modèles
    print("\n2️⃣ Modèles disponibles...")
    models = test_modeles_disponibles()
    if not models:
        print("💡 Installez un modèle: ollama pull mistral")
        return
    
    # Test 3: Test simple
    print("\n3️⃣ Test de génération...")
    if test_ollama_direct():
        print("✅ IA locale fonctionnelle !")
    else:
        print("❌ Problème avec la génération")
        return
    
    # Test 4: Conversation
    print("\n4️⃣ Mode conversation disponible")
    choix = input("Voulez-vous tester la conversation ? (y/N): ")
    if choix.lower() == 'y':
        conversation_simple()
    
    print("\n🎉 TESTS TERMINÉS - IA LOCALE OPÉRATIONNELLE !")
    print("🤖 RÉVOLUTION SILENCIEUSE ACTIVÉE !")

if __name__ == "__main__":
    main()
