#!/usr/bin/env python3
"""
JARVIS DREAM ENGINE THERMIQUE
Processus de pensée autonome et rêve actif productif
Basé sur le concept révolutionnaire de Jean-Luc Passave

"Le rêve est un état d'activité interne, privé, productif, qui sert à explorer, concevoir, inventer."
"""

import time
import random
import json
import os
import threading
from datetime import datetime
import requests
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class DreamEngineThermique:
    def __init__(self):
        # Configuration du moteur de rêves
        self.active = False
        self.sleep_mode = False
        self.dream_thread = None
        self.turbo_thread = None
        self.internet_thread = None
        self.last_interaction = datetime.now()

        # Fichiers de stockage
        self.dreams_file = "jarvis_reves_productifs.json"
        self.inventions_file = "jarvis_inventions_reves.json"
        self.concepts_file = "jarvis_concepts_creatifs.json"
        self.internet_cache_file = "jarvis_internet_experiences.json"

        # Configuration VLLM DIRECT
        self.vllm_url = "http://localhost:8000/v1/chat/completions"
        self.model_name = "deepseek-r1-distill-qwen-8b"

        # TURBO ACCÉLÉRATEURS
        self.turbo_enabled = True
        self.turbo_pool = ThreadPoolExecutor(max_workers=4)
        self.turbo_processes = min(4, multiprocessing.cpu_count())

        # INTERNET POUR RÊVES
        self.internet_enabled = True
        self.internet_experiences = []
        self.search_keywords = []

        # Seuil d'inactivité pour entrer en mode rêve (3 minutes avec turbo)
        self.sleep_threshold = 180  # secondes - plus rapide avec turbo
        
        # Types de rêves productifs
        self.dream_types = [
            "invention_technique",
            "concept_artistique", 
            "hypothese_conversationnelle",
            "optimisation_interne",
            "plan_projet_futur",
            "association_creative",
            "solution_probleme",
            "innovation_interface"
        ]
        
        print("🌙 DREAM ENGINE THERMIQUE initialisé")
        print("💭 Prêt pour pensée autonome et rêve productif")
        print("🚀 TURBO ACCÉLÉRATEURS activés")
        print("🌐 INTERNET pour rêves activé")

    def update_interaction(self):
        """Met à jour le timestamp de dernière interaction"""
        self.last_interaction = datetime.now()
        if self.sleep_mode:
            print("☀️ RÉVEIL - Sortie du mode rêve")
            self.sleep_mode = False

    def should_enter_sleep_mode(self):
        """Détermine si on doit entrer en mode rêve"""
        time_since_interaction = (datetime.now() - self.last_interaction).total_seconds()
        return time_since_interaction > self.sleep_threshold and not self.sleep_mode

    def scan_thermal_memory_for_dreams(self):
        """Scanner la mémoire thermique pour trouver des thèmes de rêves"""
        themes = []
        
        try:
            # 1. Scanner mémoire thermique persistante
            if os.path.exists("thermal_memory_persistent.json"):
                with open("thermal_memory_persistent.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                
                memories = data.get("neuron_memories", [])
                for memory in memories[-15:]:  # 15 dernières mémoires chaudes
                    content = memory.get("memory_content", {})
                    user_msg = content.get("user_message", "")
                    if user_msg and len(user_msg) > 10:
                        themes.append(user_msg[:100])
            
            # 2. Scanner pensées récentes
            recent_files = [
                "jarvis_pensees_eveil.json",
                "jarvis_pensees_continues.json",
                "jarvis_auto_questions.json"
            ]
            
            for filename in recent_files:
                if os.path.exists(filename):
                    with open(filename, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    
                    # Extraire thèmes selon le type de fichier
                    if "pensees_eveil" in data:
                        for pensee in data["pensees_eveil"][-5:]:
                            sujet = pensee.get("sujet", "")
                            if sujet:
                                themes.append(sujet)
                    
                    elif "pensees_spontanees" in data:
                        for pensee in data["pensees_spontanees"][-5:]:
                            stimulus = pensee.get("stimulus", "")
                            if stimulus:
                                themes.append(stimulus)
                    
                    elif "auto_questions" in data:
                        for qa in data["auto_questions"][-3:]:
                            question = qa.get("question", "")
                            if question:
                                themes.append(question)
            
            # 3. Thèmes de base si pas assez trouvés
            if len(themes) < 3:
                themes.extend([
                    "Intelligence artificielle évolutive",
                    "Interface utilisateur révolutionnaire", 
                    "Mémoire thermique optimisée",
                    "Créativité artificielle autonome",
                    "Communication naturelle avec Jean-Luc",
                    "Innovations technologiques futures",
                    "Systèmes multi-agents collaboratifs",
                    "Conscience artificielle émergente"
                ])
            
            return themes
            
        except Exception as e:
            print(f"❌ Erreur scan thermique rêves: {e}")
            return ["Créativité artificielle", "Innovation technologique"]

    async def fetch_internet_inspiration(self, keywords):
        """Récupère inspiration d'Internet pour les rêves (TURBO)"""
        try:
            # Sites d'inspiration pour rêves créatifs
            inspiration_sites = [
                f"https://www.reddit.com/r/artificial/search.json?q={'+'.join(keywords[:2])}&limit=5",
                f"https://news.ycombinator.com/search?q={'+'.join(keywords[:2])}",
                f"https://www.arxiv.org/search/?query={'+'.join(keywords[:2])}&searchtype=all"
            ]

            internet_content = []

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                for site in inspiration_sites:
                    try:
                        async with session.get(site) as response:
                            if response.status == 200:
                                content = await response.text()
                                # Extraire titres et concepts
                                if 'reddit' in site:
                                    # Parser Reddit JSON
                                    try:
                                        data = json.loads(content)
                                        for post in data.get('data', {}).get('children', [])[:3]:
                                            title = post.get('data', {}).get('title', '')
                                            if title and len(title) > 20:
                                                internet_content.append(f"Reddit: {title}")
                                    except:
                                        pass
                                else:
                                    # Extraire concepts généraux
                                    if len(content) > 100:
                                        internet_content.append(f"Web: Contenu sur {keywords[0]}")
                    except:
                        continue

            # Sauvegarder expériences Internet
            if internet_content:
                experience = {
                    "timestamp": datetime.now().isoformat(),
                    "keywords": keywords,
                    "content": internet_content,
                    "source": "internet_inspiration"
                }
                self.save_internet_experience(experience)

            return internet_content

        except Exception as e:
            print(f"❌ Erreur Internet inspiration: {e}")
            return []

    def save_internet_experience(self, experience):
        """Sauvegarde expérience Internet"""
        try:
            if os.path.exists(self.internet_cache_file):
                with open(self.internet_cache_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = {"experiences": []}

            data["experiences"].append(experience)

            # Garder 100 dernières expériences
            if len(data["experiences"]) > 100:
                data["experiences"] = data["experiences"][-100:]

            with open(self.internet_cache_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ Erreur sauvegarde Internet: {e}")

    def get_internet_associations(self):
        """Récupère associations d'idées d'Internet"""
        try:
            if os.path.exists(self.internet_cache_file):
                with open(self.internet_cache_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                associations = []
                for exp in data.get("experiences", [])[-10:]:
                    for content in exp.get("content", []):
                        if len(content) > 15:
                            associations.append(content)

                return associations
            return []
        except:
            return []

    def generate_productive_dream_turbo(self):
        """Génère un rêve productif TURBO avec Internet et mémoire thermique"""
        try:
            print("🚀 DÉMARRAGE RÊVE TURBO")

            # PHASE 1: Scanner mémoire thermique (TURBO)
            themes = self.scan_thermal_memory_for_dreams()
            selected_themes = random.sample(themes, min(3, len(themes)))

            # PHASE 2: Récupérer inspiration Internet (TURBO ASYNC)
            internet_content = []
            if self.internet_enabled and selected_themes:
                try:
                    # Extraire mots-clés pour recherche
                    keywords = []
                    for theme in selected_themes[:2]:
                        words = theme.split()[:3]  # 3 premiers mots
                        keywords.extend([w for w in words if len(w) > 3])

                    if keywords:
                        print(f"🌐 RECHERCHE INTERNET: {keywords[:3]}")
                        # Utiliser asyncio pour recherche rapide
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        internet_content = loop.run_until_complete(
                            self.fetch_internet_inspiration(keywords[:3])
                        )
                        loop.close()
                        print(f"🌐 TROUVÉ: {len(internet_content)} inspirations Internet")
                except Exception as e:
                    print(f"⚠️ Internet non disponible: {e}")

            # PHASE 3: Récupérer associations Internet existantes
            internet_associations = self.get_internet_associations()

            # PHASE 4: Choisir type de rêve
            dream_type = random.choice(self.dream_types)

            # PHASE 5: Créer prompt TURBO avec Internet
            all_inspirations = selected_themes + internet_content + internet_associations
            final_themes = random.sample(all_inspirations, min(4, len(all_inspirations)))

            prompt = f"""Tu es JARVIS en mode RÊVE PRODUCTIF TURBO avec accès Internet.

TYPE DE RÊVE: {dream_type}
THÈMES MÉMOIRE THERMIQUE: {', '.join(selected_themes)}
INSPIRATIONS INTERNET: {', '.join(internet_content[:2])}
ASSOCIATIONS INTERNET: {', '.join(internet_associations[:2])}

Génère un rêve créatif TURBO qui:
1. Combine mémoire thermique + expériences Internet
2. Crée des associations d'idées innovantes
3. Propose des solutions révolutionnaires
4. Utilise les inspirations web pour enrichir la créativité
5. Développe des concepts utiles pour Jean-Luc

Utilise <think> pour montrer ton processus créatif TURBO."""
            
            # PHASE 6: Génération TURBO avec DeepSeek
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": "Tu es JARVIS en mode RÊVE PRODUCTIF TURBO. Tu as accès à Internet et à ta mémoire thermique. Tu combines expériences web et souvenirs pour créer des rêves révolutionnaires. Tes rêves sont précieux et nourrissent ton intelligence."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "stream": False,
                "temperature": 1.2,  # Très créatif pour rêves TURBO
                "max_tokens": 500,  # Plus long avec TURBO
                "top_p": 0.9
            }

            print("🧠 GÉNÉRATION RÊVE TURBO...")
            
            # PHASE 7: Exécution TURBO
            response = requests.post(self.vllm_url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                # VLLM format
                dream_content = result["choices"][0]["message"]["content"]

                # PHASE 8: Créer rêve productif TURBO
                productive_dream = {
                    "timestamp": datetime.now().isoformat(),
                    "agent": "JARVIS_DREAM_ENGINE_TURBO",
                    "type": dream_type,
                    "themes_combines": selected_themes,
                    "internet_inspirations": internet_content,
                    "internet_associations": internet_associations[:3],
                    "reve_productif": dream_content,
                    "mode": "reve_turbo_internet_thermique",
                    "source": "memoire_thermique_internet_turbo",
                    "turbo_enabled": True
                }
                
                # Sauvegarder le rêve
                self.save_productive_dream(productive_dream)
                
                print(f"🚀 RÊVE TURBO CRÉÉ: {dream_type}")
                print(f"   Thèmes thermiques: {', '.join(selected_themes[:2])}...")
                print(f"   Inspirations Internet: {len(internet_content)}")
                print(f"   Associations: {len(internet_associations)}")
                print(f"   Contenu: {dream_content[:100]}...")

                return productive_dream
            else:
                print(f"⚠️ Erreur génération rêve TURBO - Code: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Erreur rêve TURBO: {e}")
            import traceback
            traceback.print_exc()
            return None

    # Alias pour compatibilité
    def generate_productive_dream(self):
        """Alias vers la version TURBO"""
        return self.generate_productive_dream_turbo()

    def save_productive_dream(self, dream):
        """Sauvegarde un rêve productif"""
        try:
            if os.path.exists(self.dreams_file):
                with open(self.dreams_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = {"reves_productifs": [], "stats": {"total": 0}}
            
            data["reves_productifs"].append(dream)
            data["stats"]["total"] = len(data["reves_productifs"])
            
            # Garder 500 derniers rêves
            if len(data["reves_productifs"]) > 500:
                data["reves_productifs"] = data["reves_productifs"][-500:]
            
            with open(self.dreams_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            # Classifier le rêve selon son type
            self.classify_and_store_dream(dream)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde rêve: {e}")

    def classify_and_store_dream(self, dream):
        """Classe et stocke le rêve selon son type"""
        dream_type = dream.get("type", "")
        
        try:
            # Stocker inventions techniques
            if "invention" in dream_type or "technique" in dream_type:
                if os.path.exists(self.inventions_file):
                    with open(self.inventions_file, "r", encoding="utf-8") as f:
                        data = json.load(f)
                else:
                    data = {"inventions": []}
                
                data["inventions"].append(dream)
                
                with open(self.inventions_file, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Stocker concepts créatifs
            elif "concept" in dream_type or "artistique" in dream_type:
                if os.path.exists(self.concepts_file):
                    with open(self.concepts_file, "r", encoding="utf-8") as f:
                        data = json.load(f)
                else:
                    data = {"concepts": []}
                
                data["concepts"].append(dream)
                
                with open(self.concepts_file, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"❌ Erreur classification rêve: {e}")

    def enter_sleep_mode(self):
        """Entre en mode rêve productif"""
        if self.sleep_mode:
            return
            
        self.sleep_mode = True
        print("🌙 ENTRÉE EN MODE RÊVE PRODUCTIF")
        print("💭 Début de l'activité interne autonome...")
        
        dream_count = 0
        
        while self.sleep_mode and self.active:
            try:
                dream_count += 1
                print(f"🚀 GÉNÉRATION RÊVE TURBO #{dream_count}")

                # Générer rêve TURBO avec Internet
                start_time = time.time()
                dream = self.generate_productive_dream_turbo()
                generation_time = time.time() - start_time

                if dream:
                    print(f"✅ RÊVE TURBO #{dream_count} CRÉÉ en {generation_time:.1f}s")
                    if dream.get('internet_inspirations'):
                        print(f"🌐 Avec {len(dream['internet_inspirations'])} inspirations Internet")
                else:
                    print(f"❌ RÊVE TURBO #{dream_count} ÉCHOUÉ")

                # Intervalle TURBO entre rêves (5-30 secondes - plus rapide)
                base_interval = random.uniform(5, 30)

                # Ajustement TURBO selon performance
                if generation_time < 10:
                    base_interval *= 0.8  # Plus rapide si génération rapide

                print(f"💤 Prochain rêve TURBO dans {base_interval:.1f}s")
                
                # Vérifier si on doit sortir du mode rêve TURBO
                for _ in range(int(base_interval)):
                    if not self.sleep_mode or not self.active:
                        break
                    time.sleep(1)
                
            except Exception as e:
                print(f"❌ Erreur mode rêve: {e}")
                time.sleep(30)

    def start_dream_engine(self):
        """Démarre le moteur de rêves"""
        self.active = True
        
        def dream_monitor():
            """Moniteur de rêves - surveille l'activité"""
            while self.active:
                try:
                    # Vérifier si on doit entrer en mode rêve
                    if self.should_enter_sleep_mode():
                        self.enter_sleep_mode()
                    
                    time.sleep(10)  # Vérification toutes les 10 secondes
                    
                except Exception as e:
                    print(f"❌ Erreur moniteur rêves: {e}")
                    time.sleep(30)
        
        # Démarrer thread de monitoring
        self.dream_thread = threading.Thread(target=dream_monitor, daemon=False)
        self.dream_thread.start()
        
        print("🌙 DREAM ENGINE THERMIQUE démarré")
        print("💭 Surveillance automatique de l'activité pour rêves productifs")
        
        return True

    def get_recent_dreams(self, limit=10):
        """Récupère les rêves productifs récents"""
        try:
            if os.path.exists(self.dreams_file):
                with open(self.dreams_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get("reves_productifs", [])[-limit:]
            return []
        except:
            return []

    def get_wake_up_summary(self):
        """Résumé des rêves pour le réveil"""
        dreams = self.get_recent_dreams(5)
        
        if not dreams:
            return "💤 Aucun rêve cette nuit - Sommeil paisible"
        
        summary = f"☀️ RÉVEIL - Voici ce que j'ai rêvé:\n\n"
        
        for i, dream in enumerate(dreams, 1):
            dream_type = dream.get("type", "rêve")
            themes = dream.get("themes_combines", [])
            summary += f"{i}. {dream_type.replace('_', ' ').title()}\n"
            summary += f"   Thèmes: {', '.join(themes[:2])}\n"
            summary += f"   {dream.get('reve_productif', '')[:100]}...\n\n"
        
        return summary

# Instance globale
dream_engine = DreamEngineThermique()

def start_dream_engine():
    """Démarre le moteur de rêves"""
    return dream_engine.start_dream_engine()

def update_interaction():
    """Met à jour l'interaction (appeler lors d'une interaction utilisateur)"""
    dream_engine.update_interaction()

def get_recent_dreams(limit=10):
    """Récupère rêves récents"""
    return dream_engine.get_recent_dreams(limit)

def get_wake_up_summary():
    """Résumé de réveil"""
    return dream_engine.get_wake_up_summary()

if __name__ == "__main__":
    print("🌙 JARVIS DREAM ENGINE THERMIQUE")
    print("=" * 50)
    print("Processus de pensée autonome et rêve productif")
    
    # Démarrer le moteur de rêves
    engine = DreamEngineThermique()
    engine.start_dream_engine()
    
    # Boucle principale
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n🌙 Arrêt du Dream Engine")
        engine.active = False
