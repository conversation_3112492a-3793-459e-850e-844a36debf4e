#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - SERVICE AUDIO
Jean-Luc Passave - 2025
Service audio avec reconnaissance vocale et synthèse
"""

import json
import os
import base64
import tempfile
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Imports audio (avec gestion d'erreur)
try:
    import speech_recognition as sr
    SR_AVAILABLE = True
except ImportError:
    SR_AVAILABLE = False
    print("⚠️ speech_recognition non disponible - Mode simulation STT")

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("⚠️ pyttsx3 non disponible - Mode simulation TTS")

class AudioService:
    """Service audio pour JARVIS V2 PRO"""
    
    def __init__(self, data_dir: str = "data/audio"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration par défaut
        self.config = {
            "language": "fr-FR",
            "tts_rate": 150,
            "tts_volume": 0.8,
            "tts_voice": "female",
            "stt_timeout": 5,
            "stt_phrase_timeout": 1,
            "audio_format": "wav"
        }
        
        # Initialiser les composants
        self.recognizer = None
        self.tts_engine = None
        self._initialize_components()
        
        # Charger la configuration
        self.load_config()
    
    def load_config(self):
        """Charge la configuration audio"""
        try:
            config_file = self.data_dir / "audio_config.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            print(f"❌ Erreur chargement config audio: {e}")
    
    def save_config(self):
        """Sauvegarde la configuration audio"""
        try:
            config_file = self.data_dir / "audio_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde config audio: {e}")
    
    def _initialize_components(self):
        """Initialise les composants audio"""
        
        # Initialiser la reconnaissance vocale
        if SR_AVAILABLE:
            try:
                self.recognizer = sr.Recognizer()
                print("🎤 Reconnaissance vocale initialisée")
            except Exception as e:
                print(f"❌ Erreur init reconnaissance vocale: {e}")
                self.recognizer = None
        
        # Initialiser la synthèse vocale
        if TTS_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                self._configure_tts()
                print("🔊 Synthèse vocale initialisée")
            except Exception as e:
                print(f"❌ Erreur init synthèse vocale: {e}")
                self.tts_engine = None
    
    def _configure_tts(self):
        """Configure le moteur TTS"""
        if not self.tts_engine:
            return
        
        try:
            # Configurer la vitesse
            self.tts_engine.setProperty('rate', self.config['tts_rate'])
            
            # Configurer le volume
            self.tts_engine.setProperty('volume', self.config['tts_volume'])
            
            # Configurer la voix
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Chercher une voix française
                for voice in voices:
                    if 'fr' in voice.id.lower() or 'french' in voice.name.lower():
                        if self.config['tts_voice'] == 'female' and 'female' in voice.name.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
                        elif self.config['tts_voice'] == 'male' and 'male' in voice.name.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
        
        except Exception as e:
            print(f"❌ Erreur configuration TTS: {e}")
    
    def speech_to_text(self, audio_data: str, language: str = None) -> Optional[str]:
        """Convertit l'audio en texte"""
        
        if language is None:
            language = self.config['language']
        
        if not self.recognizer or not SR_AVAILABLE:
            # Mode simulation
            print("🎤 [SIMULATION] Reconnaissance vocale")
            # Simuler une reconnaissance basée sur la taille des données
            audio_bytes = base64.b64decode(audio_data)
            if len(audio_bytes) > 1000:
                return "commande vocale simulée"
            else:
                return None
        
        try:
            # Décoder l'audio base64
            audio_bytes = base64.b64decode(audio_data)
            
            # Créer un fichier temporaire
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_path = temp_file.name
            
            # Reconnaissance vocale
            with sr.AudioFile(temp_path) as source:
                audio = self.recognizer.record(source)
            
            # Reconnaissance avec Google
            text = self.recognizer.recognize_google(audio, language=language)
            
            # Nettoyer le fichier temporaire
            os.unlink(temp_path)
            
            print(f"🎤 Texte reconnu: {text}")
            return text
            
        except sr.UnknownValueError:
            print("🎤 Aucune parole détectée")
            return None
        except sr.RequestError as e:
            print(f"❌ Erreur service reconnaissance: {e}")
            return None
        except Exception as e:
            print(f"❌ Erreur reconnaissance vocale: {e}")
            return None
    
    def text_to_speech(self, text: str, config: Dict[str, Any] = None) -> str:
        """Convertit le texte en audio"""
        
        if not self.tts_engine or not TTS_AVAILABLE:
            # Mode simulation
            print(f"🔊 [SIMULATION] Synthèse vocale: {text[:50]}...")
            # Retourner des données audio simulées
            simulated_audio = b"SIMULATED_AUDIO_DATA_" + text.encode()[:100]
            return base64.b64encode(simulated_audio).decode()
        
        try:
            # Appliquer la configuration temporaire si fournie
            if config:
                original_rate = self.tts_engine.getProperty('rate')
                original_volume = self.tts_engine.getProperty('volume')
                
                if 'rate' in config:
                    self.tts_engine.setProperty('rate', config['rate'])
                if 'volume' in config:
                    self.tts_engine.setProperty('volume', config['volume'])
            
            # Créer un fichier temporaire pour l'audio
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # Sauvegarder vers le fichier
            self.tts_engine.save_to_file(text, temp_path)
            self.tts_engine.runAndWait()
            
            # Lire le fichier audio
            with open(temp_path, 'rb') as f:
                audio_data = f.read()
            
            # Nettoyer le fichier temporaire
            os.unlink(temp_path)
            
            # Restaurer la configuration originale
            if config:
                self.tts_engine.setProperty('rate', original_rate)
                self.tts_engine.setProperty('volume', original_volume)
            
            # Encoder en base64
            audio_base64 = base64.b64encode(audio_data).decode()
            
            print(f"🔊 Audio généré: {len(audio_data)} bytes")
            return audio_base64
            
        except Exception as e:
            print(f"❌ Erreur synthèse vocale: {e}")
            # Fallback sur simulation
            simulated_audio = b"ERROR_AUDIO_DATA_" + text.encode()[:100]
            return base64.b64encode(simulated_audio).decode()
    
    def speak_directly(self, text: str) -> bool:
        """Parle directement (sans retourner l'audio)"""
        
        if not self.tts_engine or not TTS_AVAILABLE:
            print(f"🔊 [SIMULATION] JARVIS dit: {text}")
            return True
        
        try:
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
            print(f"🔊 JARVIS a dit: {text}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur parole directe: {e}")
            return False
    
    def listen_microphone(self, timeout: int = None) -> Optional[str]:
        """Écoute depuis le microphone"""
        
        if timeout is None:
            timeout = self.config['stt_timeout']
        
        if not self.recognizer or not SR_AVAILABLE:
            print("🎤 [SIMULATION] Écoute microphone")
            return "commande microphone simulée"
        
        try:
            with sr.Microphone() as source:
                print("🎤 Écoute en cours...")
                
                # Ajuster pour le bruit ambiant
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                # Écouter
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout,
                    phrase_time_limit=self.config['stt_phrase_timeout']
                )
            
            # Reconnaissance
            text = self.recognizer.recognize_google(
                audio, 
                language=self.config['language']
            )
            
            print(f"🎤 Entendu: {text}")
            return text
            
        except sr.WaitTimeoutError:
            print("⏰ Timeout d'écoute")
            return None
        except sr.UnknownValueError:
            print("❓ Parole non comprise")
            return None
        except Exception as e:
            print(f"❌ Erreur écoute microphone: {e}")
            return None
    
    def get_configuration(self) -> Dict[str, Any]:
        """Récupère la configuration audio"""
        return self.config.copy()
    
    def mettre_a_jour_configuration(self, nouvelle_config: Dict[str, Any]) -> Dict[str, Any]:
        """Met à jour la configuration audio"""
        
        # Mettre à jour la config
        self.config.update(nouvelle_config)
        
        # Reconfigurer le TTS si nécessaire
        if self.tts_engine and any(key.startswith('tts_') for key in nouvelle_config):
            self._configure_tts()
        
        # Sauvegarder
        self.save_config()
        
        print("🔧 Configuration audio mise à jour")
        return self.config.copy()
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Récupère les voix disponibles"""
        
        if not self.tts_engine or not TTS_AVAILABLE:
            return [
                {"id": "simulation", "name": "Voix simulée", "language": "fr-FR"}
            ]
        
        try:
            voices = self.tts_engine.getProperty('voices')
            available_voices = []
            
            for voice in voices:
                available_voices.append({
                    "id": voice.id,
                    "name": voice.name,
                    "language": getattr(voice, 'languages', ['unknown'])[0] if hasattr(voice, 'languages') else 'unknown'
                })
            
            return available_voices
            
        except Exception as e:
            print(f"❌ Erreur récupération voix: {e}")
            return []
    
    def test_audio_system(self) -> Dict[str, Any]:
        """Test du système audio"""
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "components": {
                "speech_recognition": SR_AVAILABLE,
                "text_to_speech": TTS_AVAILABLE
            },
            "tests": {}
        }
        
        # Test TTS
        if self.tts_engine:
            try:
                test_text = "Test du système audio JARVIS"
                audio_data = self.text_to_speech(test_text)
                results["tests"]["tts"] = {
                    "success": True,
                    "audio_size": len(audio_data),
                    "test_text": test_text
                }
            except Exception as e:
                results["tests"]["tts"] = {
                    "success": False,
                    "error": str(e)
                }
        else:
            results["tests"]["tts"] = {
                "success": False,
                "error": "TTS non disponible"
            }
        
        # Test STT (simulation)
        if self.recognizer:
            results["tests"]["stt"] = {
                "success": True,
                "message": "STT disponible"
            }
        else:
            results["tests"]["stt"] = {
                "success": False,
                "error": "STT non disponible"
            }
        
        # Test voix disponibles
        voices = self.get_available_voices()
        results["available_voices"] = len(voices)
        results["voice_list"] = voices[:5]  # Limiter à 5 pour l'affichage
        
        return results
