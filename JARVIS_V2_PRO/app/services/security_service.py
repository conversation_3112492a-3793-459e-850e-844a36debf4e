#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - SERVICE SÉCURITÉ
Jean-<PERSON> - 2025
Service de sécurité avec reconnaissance vocale/faciale
AUCUN BLOCAGE - VERSION PERSONNELLE
"""

import json
import os
import hashlib
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import base64

# Imports pour reconnaissance (avec gestion d'erreur)
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("⚠️ OpenCV non disponible - Mode simulation reconnaissance faciale")

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    print("⚠️ Librosa non disponible - Mode simulation reconnaissance vocale")

class SecurityService:
    """Service de sécurité pour JARVIS V2 PRO"""
    
    def __init__(self, data_dir: str = "data/security"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Fichiers de données
        self.voice_data_file = self.data_dir / "voice_profiles.json"
        self.face_data_file = self.data_dir / "face_profiles.json"
        self.config_file = self.data_dir / "security_config.json"
        self.logs_file = self.data_dir / "security_logs.json"
        
        # Données en mémoire
        self.voice_profiles = {}
        self.face_profiles = {}
        self.security_config = {
            "voice_auth_enabled": True,
            "face_auth_enabled": True,
            "require_both": False,
            "max_attempts": 3,
            "lockout_duration": 300,
            "voice_threshold": 0.7,
            "face_threshold": 0.8
        }
        self.security_logs = []
        
        # Charger les données
        self.charger_donnees()
    
    def charger_donnees(self):
        """Charge les données de sécurité"""
        try:
            # Charger les profils vocaux
            if self.voice_data_file.exists():
                with open(self.voice_data_file, 'r', encoding='utf-8') as f:
                    self.voice_profiles = json.load(f)
            
            # Charger les profils faciaux
            if self.face_data_file.exists():
                with open(self.face_data_file, 'r', encoding='utf-8') as f:
                    self.face_profiles = json.load(f)
            
            # Charger la configuration
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_loaded = json.load(f)
                    self.security_config.update(config_loaded)
            
            # Charger les logs
            if self.logs_file.exists():
                with open(self.logs_file, 'r', encoding='utf-8') as f:
                    self.security_logs = json.load(f)
            
            print(f"🔒 Sécurité chargée: {len(self.voice_profiles)} voix, {len(self.face_profiles)} visages")
            
        except Exception as e:
            print(f"❌ Erreur chargement sécurité: {e}")
    
    def sauvegarder_donnees(self):
        """Sauvegarde les données de sécurité"""
        try:
            # Sauvegarder les profils vocaux
            with open(self.voice_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.voice_profiles, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder les profils faciaux
            with open(self.face_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.face_profiles, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder la configuration
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.security_config, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder les logs (garder seulement les 1000 derniers)
            with open(self.logs_file, 'w', encoding='utf-8') as f:
                json.dump(self.security_logs[-1000:], f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde sécurité: {e}")
    
    def _log_security_event(self, event_type: str, user_identifier: str, 
                           success: bool, details: Dict[str, Any] = None):
        """Enregistre un événement de sécurité"""
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'user_identifier': user_identifier,
            'success': success,
            'details': details or {}
        }
        
        self.security_logs.append(log_entry)
        
        # Sauvegarder immédiatement les logs critiques
        if not success or event_type in ['failed_auth', 'security_breach']:
            self.sauvegarder_donnees()
    
    # === [ RECONNAISSANCE VOCALE ] ===
    
    def enregistrer_voix(self, user_identifier: str, audio_data: bytes) -> Dict[str, Any]:
        """Enregistre la voix d'un utilisateur"""
        
        try:
            # Extraire les caractéristiques vocales
            voice_features = self._extraire_caracteristiques_vocales(audio_data)
            
            # Créer le profil vocal
            voice_profile = {
                'user_identifier': user_identifier,
                'features': voice_features,
                'created_at': datetime.now().isoformat(),
                'samples_count': 1,
                'last_updated': datetime.now().isoformat()
            }
            
            # Générer un ID unique
            voice_id = hashlib.sha256(f"{user_identifier}_{datetime.now().isoformat()}".encode()).hexdigest()[:16]
            
            # Stocker le profil
            self.voice_profiles[user_identifier] = voice_profile
            
            # Sauvegarder
            self.sauvegarder_donnees()
            
            # Log de l'événement
            self._log_security_event('voice_registration', user_identifier, True, {
                'voice_id': voice_id,
                'features_count': len(voice_features)
            })
            
            print(f"🎤 Voix enregistrée: {user_identifier}")
            
            return {
                'voice_id': voice_id,
                'features_extracted': len(voice_features),
                'quality_score': self._evaluer_qualite_audio(audio_data)
            }
            
        except Exception as e:
            self._log_security_event('voice_registration', user_identifier, False, {
                'error': str(e)
            })
            raise Exception(f"Erreur enregistrement voix: {e}")
    
    def authentifier_voix(self, user_identifier: str, audio_data: bytes) -> Dict[str, Any]:
        """Authentifie un utilisateur par sa voix"""
        
        try:
            # Vérifier si l'utilisateur a un profil vocal
            if user_identifier not in self.voice_profiles:
                self._log_security_event('voice_auth', user_identifier, False, {
                    'reason': 'no_profile'
                })
                return {
                    'authenticated': False,
                    'confidence': 0.0,
                    'message': 'Aucun profil vocal enregistré pour cet utilisateur'
                }
            
            # Extraire les caractéristiques de l'audio
            current_features = self._extraire_caracteristiques_vocales(audio_data)
            
            # Comparer avec le profil enregistré
            stored_features = self.voice_profiles[user_identifier]['features']
            similarity = self._calculer_similarite_vocale(current_features, stored_features)
            
            # Déterminer l'authentification
            threshold = self.security_config['voice_threshold']
            authenticated = similarity >= threshold
            
            # Log de l'événement
            self._log_security_event('voice_auth', user_identifier, authenticated, {
                'similarity': similarity,
                'threshold': threshold
            })
            
            message = "Authentification vocale réussie" if authenticated else "Voix non reconnue"
            
            return {
                'authenticated': authenticated,
                'confidence': similarity,
                'message': message,
                'threshold_used': threshold
            }
            
        except Exception as e:
            self._log_security_event('voice_auth', user_identifier, False, {
                'error': str(e)
            })
            return {
                'authenticated': False,
                'confidence': 0.0,
                'message': f'Erreur authentification vocale: {e}'
            }
    
    def _extraire_caracteristiques_vocales(self, audio_data: bytes) -> List[float]:
        """Extrait les caractéristiques vocales d'un audio"""
        
        if not LIBROSA_AVAILABLE:
            # Mode simulation
            print("🎤 [SIMULATION] Extraction caractéristiques vocales")
            # Générer des caractéristiques simulées basées sur les données
            hash_audio = hashlib.md5(audio_data).hexdigest()
            features = [float(int(hash_audio[i:i+2], 16)) / 255.0 for i in range(0, 20, 2)]
            return features
        
        try:
            # En production, utiliser librosa pour extraire les MFCC
            import tempfile
            import soundfile as sf
            
            # Sauvegarder temporairement l'audio
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            # Charger l'audio
            y, sr = librosa.load(temp_path, sr=22050)
            
            # Extraire les MFCC (Mel-frequency cepstral coefficients)
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            
            # Calculer la moyenne des MFCC
            features = np.mean(mfccs, axis=1).tolist()
            
            # Nettoyer le fichier temporaire
            os.unlink(temp_path)
            
            return features
            
        except Exception as e:
            print(f"❌ Erreur extraction vocale: {e}")
            # Fallback sur simulation
            hash_audio = hashlib.md5(audio_data).hexdigest()
            features = [float(int(hash_audio[i:i+2], 16)) / 255.0 for i in range(0, 20, 2)]
            return features
    
    def _calculer_similarite_vocale(self, features1: List[float], features2: List[float]) -> float:
        """Calcule la similarité entre deux ensembles de caractéristiques vocales"""
        
        try:
            # Convertir en arrays numpy
            f1 = np.array(features1)
            f2 = np.array(features2)
            
            # Calculer la distance cosinus
            dot_product = np.dot(f1, f2)
            norm_f1 = np.linalg.norm(f1)
            norm_f2 = np.linalg.norm(f2)
            
            if norm_f1 == 0 or norm_f2 == 0:
                return 0.0
            
            cosine_similarity = dot_product / (norm_f1 * norm_f2)
            
            # Convertir en pourcentage (0-1)
            similarity = (cosine_similarity + 1) / 2
            
            return float(similarity)
            
        except Exception as e:
            print(f"❌ Erreur calcul similarité vocale: {e}")
            return 0.0
    
    def verifier_qualite_audio(self, audio_data: bytes) -> Dict[str, Any]:
        """Vérifie la qualité d'un échantillon audio"""
        
        try:
            # Vérifications de base
            taille = len(audio_data)
            
            quality_score = 1.0
            issues = []
            
            # Vérifier la taille
            if taille < 1000:  # Moins de 1KB
                quality_score -= 0.3
                issues.append("Audio trop court")
            elif taille > 10000000:  # Plus de 10MB
                quality_score -= 0.2
                issues.append("Audio très volumineux")
            
            # Vérifier le contenu (simulation)
            if len(set(audio_data[:100])) < 10:  # Peu de variation
                quality_score -= 0.4
                issues.append("Audio peu varié (possiblement silence)")
            
            quality_score = max(0.0, quality_score)
            
            return {
                'quality_score': quality_score,
                'file_size': taille,
                'issues': issues,
                'recommendations': self._generer_recommandations_audio(quality_score, issues)
            }
            
        except Exception as e:
            return {
                'quality_score': 0.0,
                'file_size': len(audio_data),
                'issues': [f"Erreur analyse: {e}"],
                'recommendations': ["Réenregistrer l'audio"]
            }
    
    def _evaluer_qualite_audio(self, audio_data: bytes) -> float:
        """Évalue la qualité d'un audio (score 0-1)"""
        verification = self.verifier_qualite_audio(audio_data)
        return verification['quality_score']
    
    def _generer_recommandations_audio(self, quality_score: float, issues: List[str]) -> List[str]:
        """Génère des recommandations pour améliorer la qualité audio"""
        
        recommendations = []
        
        if quality_score < 0.5:
            recommendations.append("Réenregistrer dans un environnement plus silencieux")
        
        if "Audio trop court" in issues:
            recommendations.append("Enregistrer un échantillon plus long (au moins 3 secondes)")
        
        if "Audio peu varié" in issues:
            recommendations.append("Parler plus clairement et varier l'intonation")
        
        if quality_score > 0.8:
            recommendations.append("Qualité audio excellente")
        
        return recommendations
    
    # === [ RECONNAISSANCE FACIALE ] ===
    
    def enregistrer_visage(self, user_identifier: str, image_data: bytes) -> Dict[str, Any]:
        """Enregistre le visage d'un utilisateur"""
        
        try:
            # Extraire les caractéristiques faciales
            face_features = self._extraire_caracteristiques_faciales(image_data)
            
            if not face_features:
                raise Exception("Aucun visage détecté dans l'image")
            
            # Créer le profil facial
            face_profile = {
                'user_identifier': user_identifier,
                'features': face_features,
                'created_at': datetime.now().isoformat(),
                'samples_count': 1,
                'last_updated': datetime.now().isoformat()
            }
            
            # Générer un ID unique
            face_id = hashlib.sha256(f"{user_identifier}_{datetime.now().isoformat()}".encode()).hexdigest()[:16]
            
            # Stocker le profil
            self.face_profiles[user_identifier] = face_profile
            
            # Sauvegarder
            self.sauvegarder_donnees()
            
            # Log de l'événement
            self._log_security_event('face_registration', user_identifier, True, {
                'face_id': face_id,
                'features_count': len(face_features)
            })
            
            print(f"👁️ Visage enregistré: {user_identifier}")
            
            return {
                'face_id': face_id,
                'features_extracted': len(face_features),
                'quality_score': self._evaluer_qualite_image(image_data)
            }
            
        except Exception as e:
            self._log_security_event('face_registration', user_identifier, False, {
                'error': str(e)
            })
            raise Exception(f"Erreur enregistrement visage: {e}")
    
    def authentifier_visage(self, user_identifier: str, image_data: bytes) -> Dict[str, Any]:
        """Authentifie un utilisateur par son visage"""
        
        try:
            # Vérifier si l'utilisateur a un profil facial
            if user_identifier not in self.face_profiles:
                self._log_security_event('face_auth', user_identifier, False, {
                    'reason': 'no_profile'
                })
                return {
                    'authenticated': False,
                    'confidence': 0.0,
                    'message': 'Aucun profil facial enregistré pour cet utilisateur'
                }
            
            # Extraire les caractéristiques de l'image
            current_features = self._extraire_caracteristiques_faciales(image_data)
            
            if not current_features:
                return {
                    'authenticated': False,
                    'confidence': 0.0,
                    'message': 'Aucun visage détecté dans l\'image'
                }
            
            # Comparer avec le profil enregistré
            stored_features = self.face_profiles[user_identifier]['features']
            similarity = self._calculer_similarite_faciale(current_features, stored_features)
            
            # Déterminer l'authentification
            threshold = self.security_config['face_threshold']
            authenticated = similarity >= threshold
            
            # Log de l'événement
            self._log_security_event('face_auth', user_identifier, authenticated, {
                'similarity': similarity,
                'threshold': threshold
            })
            
            message = "Authentification faciale réussie" if authenticated else "Visage non reconnu"
            
            return {
                'authenticated': authenticated,
                'confidence': similarity,
                'message': message,
                'threshold_used': threshold
            }
            
        except Exception as e:
            self._log_security_event('face_auth', user_identifier, False, {
                'error': str(e)
            })
            return {
                'authenticated': False,
                'confidence': 0.0,
                'message': f'Erreur authentification faciale: {e}'
            }
    
    def _extraire_caracteristiques_faciales(self, image_data: bytes) -> Optional[List[float]]:
        """Extrait les caractéristiques faciales d'une image"""
        
        if not CV2_AVAILABLE:
            # Mode simulation
            print("👁️ [SIMULATION] Extraction caractéristiques faciales")
            # Générer des caractéristiques simulées
            hash_image = hashlib.md5(image_data).hexdigest()
            features = [float(int(hash_image[i:i+2], 16)) / 255.0 for i in range(0, 32, 2)]
            return features
        
        try:
            # Convertir les bytes en image OpenCV
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                return None
            
            # Convertir en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Détecter les visages
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                return None
            
            # Prendre le premier visage détecté
            (x, y, w, h) = faces[0]
            face_roi = gray[y:y+h, x:x+w]
            
            # Redimensionner à une taille fixe
            face_resized = cv2.resize(face_roi, (64, 64))
            
            # Extraire des caractéristiques simples (histogramme)
            hist = cv2.calcHist([face_resized], [0], None, [16], [0, 256])
            features = hist.flatten().tolist()
            
            # Normaliser
            features = [f / max(features) if max(features) > 0 else 0 for f in features]
            
            return features
            
        except Exception as e:
            print(f"❌ Erreur extraction faciale: {e}")
            # Fallback sur simulation
            hash_image = hashlib.md5(image_data).hexdigest()
            features = [float(int(hash_image[i:i+2], 16)) / 255.0 for i in range(0, 32, 2)]
            return features
    
    def _calculer_similarite_faciale(self, features1: List[float], features2: List[float]) -> float:
        """Calcule la similarité entre deux ensembles de caractéristiques faciales"""
        
        try:
            # Même méthode que pour la voix
            f1 = np.array(features1)
            f2 = np.array(features2)
            
            dot_product = np.dot(f1, f2)
            norm_f1 = np.linalg.norm(f1)
            norm_f2 = np.linalg.norm(f2)
            
            if norm_f1 == 0 or norm_f2 == 0:
                return 0.0
            
            cosine_similarity = dot_product / (norm_f1 * norm_f2)
            similarity = (cosine_similarity + 1) / 2
            
            return float(similarity)
            
        except Exception as e:
            print(f"❌ Erreur calcul similarité faciale: {e}")
            return 0.0
    
    def verifier_qualite_image(self, image_data: bytes) -> Dict[str, Any]:
        """Vérifie la qualité d'une image"""
        
        try:
            taille = len(image_data)
            
            quality_score = 1.0
            issues = []
            
            # Vérifier la taille
            if taille < 5000:  # Moins de 5KB
                quality_score -= 0.3
                issues.append("Image trop petite")
            elif taille > 5000000:  # Plus de 5MB
                quality_score -= 0.2
                issues.append("Image très volumineuse")
            
            # Vérifications avec OpenCV si disponible
            if CV2_AVAILABLE:
                try:
                    nparr = np.frombuffer(image_data, np.uint8)
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if image is not None:
                        height, width = image.shape[:2]
                        
                        if width < 100 or height < 100:
                            quality_score -= 0.4
                            issues.append("Résolution trop faible")
                        
                        # Vérifier la luminosité
                        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                        brightness = np.mean(gray)
                        
                        if brightness < 50:
                            quality_score -= 0.3
                            issues.append("Image trop sombre")
                        elif brightness > 200:
                            quality_score -= 0.2
                            issues.append("Image trop claire")
                    
                except Exception:
                    quality_score -= 0.5
                    issues.append("Format d'image invalide")
            
            quality_score = max(0.0, quality_score)
            
            return {
                'quality_score': quality_score,
                'file_size': taille,
                'issues': issues,
                'recommendations': self._generer_recommandations_image(quality_score, issues)
            }
            
        except Exception as e:
            return {
                'quality_score': 0.0,
                'file_size': len(image_data),
                'issues': [f"Erreur analyse: {e}"],
                'recommendations': ["Reprendre la photo"]
            }
    
    def _evaluer_qualite_image(self, image_data: bytes) -> float:
        """Évalue la qualité d'une image (score 0-1)"""
        verification = self.verifier_qualite_image(image_data)
        return verification['quality_score']
    
    def _generer_recommandations_image(self, quality_score: float, issues: List[str]) -> List[str]:
        """Génère des recommandations pour améliorer la qualité image"""
        
        recommendations = []
        
        if quality_score < 0.5:
            recommendations.append("Reprendre la photo dans de meilleures conditions")
        
        if "Image trop sombre" in issues:
            recommendations.append("Améliorer l'éclairage")
        
        if "Image trop claire" in issues:
            recommendations.append("Réduire l'exposition")
        
        if "Résolution trop faible" in issues:
            recommendations.append("Utiliser une résolution plus élevée")
        
        if quality_score > 0.8:
            recommendations.append("Qualité image excellente")
        
        return recommendations
    
    # === [ GESTION DES DONNÉES ] ===
    
    def lister_utilisateurs_voix(self) -> List[Dict[str, Any]]:
        """Liste les utilisateurs avec authentification vocale"""
        
        users = []
        for user_id, profile in self.voice_profiles.items():
            users.append({
                'user_identifier': user_id,
                'created_at': profile['created_at'],
                'last_updated': profile['last_updated'],
                'samples_count': profile['samples_count']
            })
        
        return users
    
    def supprimer_donnees_voix(self, user_identifier: str) -> bool:
        """Supprime les données vocales d'un utilisateur"""
        
        if user_identifier in self.voice_profiles:
            del self.voice_profiles[user_identifier]
            self.sauvegarder_donnees()
            
            self._log_security_event('voice_data_deleted', user_identifier, True)
            return True
        
        return False
    
    def supprimer_donnees_visage(self, user_identifier: str) -> bool:
        """Supprime les données faciales d'un utilisateur"""
        
        if user_identifier in self.face_profiles:
            del self.face_profiles[user_identifier]
            self.sauvegarder_donnees()
            
            self._log_security_event('face_data_deleted', user_identifier, True)
            return True
        
        return False
    
    def get_configuration(self) -> Dict[str, Any]:
        """Récupère la configuration sécurité"""
        return self.security_config.copy()
    
    def mettre_a_jour_configuration(self, nouvelle_config: Dict[str, Any]) -> Dict[str, Any]:
        """Met à jour la configuration sécurité"""
        
        self.security_config.update(nouvelle_config)
        self.sauvegarder_donnees()
        
        self._log_security_event('config_updated', 'system', True, nouvelle_config)
        
        return self.security_config.copy()
    
    def get_statut_securite(self) -> Dict[str, Any]:
        """Retourne le statut du système de sécurité"""
        
        return {
            'voice_profiles_count': len(self.voice_profiles),
            'face_profiles_count': len(self.face_profiles),
            'security_logs_count': len(self.security_logs),
            'config': self.security_config,
            'components_available': {
                'opencv': CV2_AVAILABLE,
                'librosa': LIBROSA_AVAILABLE
            },
            'last_activity': self.security_logs[-1]['timestamp'] if self.security_logs else None
        }
