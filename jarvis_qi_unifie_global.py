#!/usr/bin/env python3
"""
🧠 JARVIS QI UNIFIÉ GLOBAL - CORRECTION INCOHÉRENCE
==================================================

Système unifié pour corriger l'incohérence du coefficient intellectuel
entre le Dashboard (159) et les autres parties du code.

OBJECTIF: Un seul système de QI pour toutes les interfaces
Auteur: Jean<PERSON><PERSON> + Claude
Date: 2025-06-21
"""

import json
import time
from datetime import datetime
from pathlib import Path

class JarvisQIUnifieGlobal:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.qi_config_file = self.base_dir / "jarvis_qi_config_global.json"
        
        # Configuration QI unifiée
        self.qi_config = self.charger_config_qi()
        
        print("🧠 JARVIS QI UNIFIÉ GLOBAL - Initialisation")
        print(f"📊 QI Actuel: {self.get_qi_unifie()}")
        print(f"🧬 Neurones Actifs: {self.get_neurones_actifs():,}")
        
    def charger_config_qi(self):
        """Charger la configuration QI unifiée"""
        if self.qi_config_file.exists():
            with open(self.qi_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # Configuration par défaut basée sur les valeurs actuelles
        config_defaut = {
            "qi_unifie": {
                "valeur_actuelle": 164.0,  # QI JARVIS CORRECT - JEAN-LUC PASSAVE
                "methode_calcul": "evolutif_ict",
                "derniere_mise_a_jour": datetime.now().isoformat()
            },
            "neurones": {
                "total": 89000000000,      # 89 milliards
                "actifs": 89067389,        # Valeur du Dashboard
                "croissance_par_minute": 1000,
                "derniere_evolution": datetime.now().isoformat()
            },
            "facteurs_calcul": {
                "qi_base": 164.0,  # QI JARVIS CORRECT - JEAN-LUC PASSAVE
                "bonus_neurones": 0.0001,  # Par neurone actif
                "bonus_memoire": 2.0,      # Par conversation
                "bonus_modules": 5.0,      # Par module chargé
                "bonus_creativite": 10.0,  # Par capacité créative
                "bonus_thermique": 15.0    # Niveau thermique max
            },
            "limites": {
                "qi_minimum": 85.0,
                "qi_maximum": 300.0,
                "evolution_max_par_heure": 5.0
            },
            "historique_evolution": []
        }
        
        self.sauvegarder_config_qi(config_defaut)
        return config_defaut
        
    def sauvegarder_config_qi(self, config=None):
        """Sauvegarder la configuration QI"""
        if config is None:
            config = self.qi_config
            
        with open(self.qi_config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
    def get_qi_unifie(self):
        """Obtenir le QI unifié actuel"""
        return self.qi_config["qi_unifie"]["valeur_actuelle"]
        
    def get_neurones_actifs(self):
        """Obtenir le nombre de neurones actifs"""
        return self.qi_config["neurones"]["actifs"]
        
    def get_neurones_total(self):
        """Obtenir le nombre total de neurones"""
        return self.qi_config["neurones"]["total"]
        
    def calculer_qi_evolutif(self):
        """Calculer le QI selon la méthode évolutive - PRIORITÉ ICT"""
        try:
            # PRIORITÉ 1: Système ICT (Indice Cognitif Thermique) - Conseils ChatGPT
            try:
                from jarvis_intelligence_thermique_avancee import IntelligenceThermique
                intel_thermique = IntelligenceThermique(qi_initial=120.0)
                qi_thermique = intel_thermique.calculer_qi_thermique()
                if qi_thermique and qi_thermique > 0:
                    print(f"✅ QI ICT calculé: {qi_thermique:.1f}")
                    return qi_thermique
            except ImportError:
                print("⚠️ Système ICT non disponible")
                pass

            # PRIORITÉ 2: Système QI Central ICT
            try:
                from jarvis_qi_central_ict import get_qi_unifie as get_qi_ict
                qi_ict = get_qi_ict()
                if qi_ict and qi_ict > 0:
                    print(f"✅ QI Central ICT: {qi_ict:.1f}")
                    return qi_ict
            except ImportError:
                print("⚠️ QI Central ICT non disponible")
                pass
                
            # Calcul de base si les systèmes avancés ne sont pas disponibles
            facteurs = self.qi_config["facteurs_calcul"]
            neurones = self.qi_config["neurones"]
            
            qi_base = facteurs["qi_base"]
            bonus_neurones = neurones["actifs"] * facteurs["bonus_neurones"]
            bonus_memoire = 45 * facteurs["bonus_memoire"]  # 45 conversations
            bonus_modules = 15 * facteurs["bonus_modules"]   # 15 modules
            bonus_creativite = 8 * facteurs["bonus_creativite"]  # 8 capacités
            
            qi_total = qi_base + bonus_neurones + bonus_memoire + bonus_modules + bonus_creativite
            
            # Appliquer les limites
            qi_total = max(self.qi_config["limites"]["qi_minimum"], min(self.qi_config["limites"]["qi_maximum"], qi_total))
            
            return round(qi_total, 1)
            
        except Exception as e:
            print(f"❌ Erreur calcul QI évolutif: {e}")
            return self.qi_config["qi_unifie"]["valeur_actuelle"]
            
    def mettre_a_jour_qi(self, nouveau_qi=None, source="auto"):
        """Mettre à jour le QI unifié"""
        if nouveau_qi is None:
            nouveau_qi = self.calculer_qi_evolutif()
            
        ancien_qi = self.qi_config["qi_unifie"]["valeur_actuelle"]
        
        # Vérifier les limites d'évolution
        evolution_max = self.qi_config["limites"]["evolution_max_par_heure"]
        if abs(nouveau_qi - ancien_qi) > evolution_max:
            if nouveau_qi > ancien_qi:
                nouveau_qi = ancien_qi + evolution_max
            else:
                nouveau_qi = ancien_qi - evolution_max
                
        # Mettre à jour
        self.qi_config["qi_unifie"]["valeur_actuelle"] = nouveau_qi
        self.qi_config["qi_unifie"]["derniere_mise_a_jour"] = datetime.now().isoformat()
        
        # Ajouter à l'historique
        evolution = {
            "timestamp": datetime.now().isoformat(),
            "ancien_qi": ancien_qi,
            "nouveau_qi": nouveau_qi,
            "evolution": nouveau_qi - ancien_qi,
            "source": source
        }
        
        self.qi_config["historique_evolution"].append(evolution)
        
        # Garder seulement les 100 dernières évolutions
        if len(self.qi_config["historique_evolution"]) > 100:
            self.qi_config["historique_evolution"] = self.qi_config["historique_evolution"][-100:]
            
        self.sauvegarder_config_qi()
        
        return nouveau_qi
        
    def evoluer_neurones(self):
        """Faire évoluer le nombre de neurones"""
        croissance = self.qi_config["neurones"]["croissance_par_minute"]
        
        # Calculer le temps écoulé depuis la dernière évolution
        derniere_evolution = datetime.fromisoformat(self.qi_config["neurones"]["derniere_evolution"])
        temps_ecoule = (datetime.now() - derniere_evolution).total_seconds() / 60  # en minutes
        
        # Ajouter les nouveaux neurones
        nouveaux_neurones = int(temps_ecoule * croissance)
        if nouveaux_neurones > 0:
            self.qi_config["neurones"]["actifs"] += nouveaux_neurones
            self.qi_config["neurones"]["derniere_evolution"] = datetime.now().isoformat()
            
            print(f"🧬 Évolution neurones: +{nouveaux_neurones:,} neurones")
            print(f"🧠 Total neurones actifs: {self.qi_config['neurones']['actifs']:,}")
            
            self.sauvegarder_config_qi()
            
        return nouveaux_neurones
        
    def synchroniser_toutes_interfaces(self):
        """Synchroniser le QI dans toutes les interfaces"""
        qi_actuel = self.get_qi_unifie()
        neurones_actifs = self.get_neurones_actifs()
        
        print(f"🔄 Synchronisation QI: {qi_actuel}")
        print(f"🧬 Synchronisation neurones: {neurones_actifs:,}")
        
        # Mettre à jour le Dashboard simple
        try:
            dashboard_file = self.base_dir / "jarvis_dashboard_simple.py"
            if dashboard_file.exists():
                with open(dashboard_file, 'r', encoding='utf-8') as f:
                    contenu = f.read()
                    
                # Remplacer les valeurs
                contenu = contenu.replace('"qi_total": 159', f'"qi_total": {int(qi_actuel)}')
                contenu = contenu.replace('"neurones_actifs": 89067389', f'"neurones_actifs": {neurones_actifs}')
                
                with open(dashboard_file, 'w', encoding='utf-8') as f:
                    f.write(contenu)
                    
                print("✅ Dashboard simple synchronisé")
        except Exception as e:
            print(f"⚠️ Erreur sync dashboard simple: {e}")
            
        return {
            "qi_synchronise": qi_actuel,
            "neurones_synchronises": neurones_actifs,
            "timestamp": datetime.now().isoformat()
        }
        
    def generer_rapport_qi(self):
        """Générer un rapport complet du QI"""
        qi_actuel = self.get_qi_unifie()
        neurones_actifs = self.get_neurones_actifs()
        
        # Évolutions récentes
        evolutions_recentes = self.qi_config["historique_evolution"][-5:]
        
        rapport = {
            "qi_unifie_global": {
                "valeur_actuelle": qi_actuel,
                "neurones_actifs": neurones_actifs,
                "neurones_total": self.get_neurones_total(),
                "methode_calcul": self.qi_config["qi_unifie"]["methode_calcul"],
                "derniere_mise_a_jour": self.qi_config["qi_unifie"]["derniere_mise_a_jour"]
            },
            "evolution_recente": {
                "nombre_evolutions": len(self.qi_config["historique_evolution"]),
                "evolutions_recentes": evolutions_recentes,
                "evolution_totale": sum(e["evolution"] for e in evolutions_recentes)
            },
            "configuration": {
                "qi_minimum": self.qi_config["limites"]["qi_minimum"],
                "qi_maximum": self.qi_config["limites"]["qi_maximum"],
                "evolution_max_par_heure": self.qi_config["limites"]["evolution_max_par_heure"]
            },
            "correction_incohérence": {
                "probleme_identifie": "QI différent entre Dashboard (159) et autres interfaces",
                "solution_appliquee": "Système QI unifié global",
                "statut": "Corrigé"
            }
        }
        
        return rapport

# Instance globale
qi_unifie_global = JarvisQIUnifieGlobal()

def get_qi_global():
    """Fonction globale pour obtenir le QI unifié"""
    return qi_unifie_global.get_qi_unifie()

def get_neurones_global():
    """Fonction globale pour obtenir les neurones actifs"""
    return qi_unifie_global.get_neurones_actifs()

def synchroniser_qi_global():
    """Fonction globale pour synchroniser le QI partout"""
    return qi_unifie_global.synchroniser_toutes_interfaces()

def main():
    """Fonction principale"""
    print("🧠 JARVIS QI UNIFIÉ GLOBAL - CORRECTION INCOHÉRENCE")
    print("=" * 60)
    
    # Évolution des neurones
    nouveaux_neurones = qi_unifie_global.evoluer_neurones()
    
    # Mise à jour du QI
    nouveau_qi = qi_unifie_global.mettre_a_jour_qi()
    
    # Synchronisation
    sync_result = qi_unifie_global.synchroniser_toutes_interfaces()
    
    # Rapport
    rapport = qi_unifie_global.generer_rapport_qi()
    
    print(f"\n📊 RAPPORT FINAL:")
    print(f"🧠 QI Unifié: {rapport['qi_unifie_global']['valeur_actuelle']}")
    print(f"🧬 Neurones Actifs: {rapport['qi_unifie_global']['neurones_actifs']:,}")
    print(f"📈 Évolutions récentes: {len(rapport['evolution_recente']['evolutions_recentes'])}")
    print(f"✅ Incohérence corrigée: {rapport['correction_incohérence']['statut']}")

if __name__ == "__main__":
    main()
