#!/usr/bin/env python3
"""
🚀 ACCÉLÉRATEURS POUR JARVIS_MEMORY_COMPRESSION.PY
"""

from concurrent.futures import ThreadPoolExecutor
import gzip
import pickle
import os
import time
import json


class MemoryAccelerator:
    def __init__(self):
        self.cache = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    def accelerate_memory_access(self, key, data):
        if key in self.cache:
            return self.cache[key]
        
        compressed_data = gzip.compress(pickle.dumps(data))
        self.cache[key] = compressed_data
        return compressed_data
    
    def async_save(self, data, path):
        return self.thread_pool.submit(self._save_compressed, data, path)
    
    def _save_compressed(self, data, path):
        with gzip.open(f"{path}.accelerated", 'wb') as f:
            pickle.dump(data, f)



def cache_intelligent(max_size=1000):
    def decorator(func):
        cache = {}
        access_count = {}
        
        def wrapper(*args, **kwargs):
            key = hash(str(args) + str(kwargs))
            
            if key in cache:
                access_count[key] = access_count.get(key, 0) + 1
                return cache[key]
            
            result = func(*args, **kwargs)
            
            if len(cache) >= max_size:
                # Supprimer l'élément le moins utilisé
                least_used = min(access_count.items(), key=lambda x: x[1])[0]
                del cache[least_used]
                del access_count[least_used]
            
            cache[key] = result
            access_count[key] = 1
            return result
        
        return wrapper
    return decorator



def compression_rapide(data, level=6):
    if isinstance(data, str):
        data = data.encode('utf-8')
    elif isinstance(data, dict):
        data = json.dumps(data, separators=(',', ':')).encode('utf-8')
    
    return gzip.compress(data, compresslevel=level)

def decompression_rapide(compressed_data):
    decompressed = gzip.decompress(compressed_data)
    try:
        return json.loads(decompressed.decode('utf-8'))
    except:
        return decompressed.decode('utf-8')



def threading_optimise(max_workers=4):
    def decorator(func):
        thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        def wrapper(*args, **kwargs):
            if kwargs.get('async_mode', False):
                return thread_pool.submit(func, *args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


