#!/usr/bin/env python3
"""
JARVIS - Accélérateurs Turbo Simple
Accélérateurs de performance SANS OLLAMA
Auteur: <PERSON><PERSON><PERSON>ave
Date: 2025-06-21
"""

import time
import subprocess
import platform
import json
import os
from datetime import datetime
import requests

class AccelerateursTurboSimple:
    """Accélérateurs turbo pour JARVIS SANS OLLAMA"""
    
    def __init__(self):
        self.config = {
            "timeout_ultra": 2,
            "timeout_normal": 5,
            "timeout_max": 10
        }
        
        # Configuration VLLM DIRECT
        self.vllm_url = "http://localhost:8000/v1/chat/completions"
        self.model_name = "deepseek-r1-distill-qwen-8b"
        
        self.detecter_processeur()
        print("⚡ ACCÉLÉRATEURS TURBO initialisés")
    
    def detecter_processeur(self):
        """Détecte le type de processeur"""
        try:
            if platform.system() == "Darwin":  # macOS
                result = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'], 
                                      capture_output=True, text=True)
                if "M1" in result.stdout or "M2" in result.stdout or "M3" in result.stdout:
                    print("🚀 Processeur Apple Silicon détecté - TURBO activé")
                    self.config["timeout_ultra"] = 1
                else:
                    print("💻 Processeur Intel détecté")
            else:
                print("💻 Processeur standard détecté")
        except:
            print("💻 Processeur standard détecté")
    
    def vllm_turbo(self, prompt, timeout=2):
        """VLLM ultra-rapide avec timeout strict"""
        print(f"⚡ VLLM turbo - timeout {timeout}s")
        
        try:
            payload = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": "Tu es JARVIS en mode TURBO. Réponds rapidement et efficacement."},
                    {"role": "user", "content": prompt[:100]}  # Limiter la taille du prompt
                ],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 100
            }
            
            start_time = time.time()
            response = requests.post(self.vllm_url, json=payload, timeout=timeout)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                return {
                    "reponse": content,
                    "duree": duration,
                    "statut": "succes",
                    "model": "vllm-turbo"
                }
            else:
                print(f"❌ Erreur VLLM: {response.status_code}")
                return self.reponse_fallback(prompt, duration)
                
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout VLLM ({timeout}s)")
            return self.reponse_fallback(prompt, timeout)
        except Exception as e:
            print(f"❌ Erreur VLLM: {e}")
            return self.reponse_fallback(prompt, timeout)
    
    def reponse_fallback(self, prompt, duree):
        """🚨 PLUS DE SIMULATION - UTILISER VRAI AGENT TURBO"""
        # 🚀 CONNEXION AGENT TURBO RÉEL - JEAN-LUC PASSAVE
        try:
            from jarvis_agent2_traducteur_turbo import agent2_traducteur
            reponse_vraie = agent2_traducteur.traduire_sync(prompt, "français")
            return {
                "reponse": reponse_vraie,
                "duree": duree,
                "statut": "agent_turbo_reel",
                "model": "agent2-turbo"
            }
        except Exception as e:
            return {
                "reponse": f"🚀 Agent Turbo en connexion... {prompt[:50]}",
                "duree": duree,
                "statut": "agent_turbo_connexion",
                "model": "agent2-turbo"
            }
    
    def test_vitesse_modeles(self, prompt="Test de vitesse"):
        """Test de vitesse des modèles disponibles"""
        print("🧪 TEST VITESSE MODÈLES")
        print("=" * 40)
        
        modeles_test = ["vllm-turbo", "fallback"]
        resultats = []
        
        for model in modeles_test:
            try:
                print(f"⚡ Test {model}...")
                
                if model == "vllm-turbo":
                    start = time.time()
                    result = self.vllm_turbo(prompt, 3)
                    duree = time.time() - start
                else:
                    start = time.time()
                    result = self.reponse_fallback(prompt, 0)
                    duree = time.time() - start
                
                resultats.append({
                    "model": model,
                    "duree": duree,
                    "statut": result.get("statut", "inconnu"),
                    "reponse": result.get("reponse", "")[:50]
                })
                
                print(f"   ✅ {model}: {duree:.2f}s")
                
            except Exception as e:
                print(f"   ❌ {model}: Erreur - {e}")
                resultats.append({
                    "model": model,
                    "duree": 999,
                    "statut": "erreur",
                    "reponse": str(e)
                })
        
        # Trier par vitesse
        resultats.sort(key=lambda x: x["duree"])
        
        print(f"\n🏆 CLASSEMENT VITESSE:")
        for i, result in enumerate(resultats, 1):
            print(f"   {i}. {result['model']}: {result['duree']:.2f}s")
        
        return resultats
    
    def test_simple(self, prompt="Bonjour JARVIS"):
        """Test simple et rapide"""
        print(f"🧪 Test simple: '{prompt}'")
        
        start_time = time.time()
        result = self.vllm_turbo(prompt, self.config["timeout_ultra"])
        total_time = time.time() - start_time
        
        print(f"\n📊 RÉSULTAT TEST:")
        print(f"   ⏱️ Temps total: {total_time:.2f}s")
        print(f"   📝 Réponse: {result['reponse'][:100]}...")
        print(f"   ✅ Statut: {result['statut']}")
        
        return result
    
    def benchmark_complet(self):
        """Benchmark complet des accélérateurs"""
        print("🚀 BENCHMARK ACCÉLÉRATEURS TURBO")
        print("=" * 50)
        
        tests = [
            "Analyse rapide",
            "Optimisation système", 
            "Créativité express",
            "Solution immédiate",
            "Innovation turbo"
        ]
        
        resultats = []
        
        for i, test in enumerate(tests, 1):
            print(f"\n🧪 Test {i}/{len(tests)}: '{test}'")
            
            start = time.time()
            result = self.vllm_turbo(test, 2)  # 2s max
            duree = time.time() - start
            
            resultats.append({
                "test": test,
                "duree": duree,
                "statut": result["statut"],
                "reponse": result["reponse"]
            })
            
            print(f"   ⏱️ {duree:.2f}s - {result['statut']}")
        
        # Statistiques
        durees = [r["duree"] for r in resultats]
        duree_moyenne = sum(durees) / len(durees)
        duree_min = min(durees)
        duree_max = max(durees)
        
        print(f"\n📊 STATISTIQUES BENCHMARK:")
        print(f"   ⚡ Plus rapide: {duree_min:.2f}s")
        print(f"   🐌 Plus lent: {duree_max:.2f}s") 
        print(f"   📈 Moyenne: {duree_moyenne:.2f}s")
        
        # Sauvegarder résultats
        self.sauvegarder_benchmark(resultats)
        
        return resultats
    
    def sauvegarder_benchmark(self, resultats):
        """Sauvegarde les résultats de benchmark"""
        try:
            data = {
                "timestamp": datetime.now().isoformat(),
                "resultats": resultats,
                "config": self.config
            }
            
            with open("jarvis_benchmark_turbo.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Benchmark sauvegardé: jarvis_benchmark_turbo.json")
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

if __name__ == "__main__":
    print("⚡ JARVIS ACCÉLÉRATEURS TURBO")
    print("=" * 40)
    print("Tests de performance SANS OLLAMA")
    
    accelerateurs = AccelerateursTurboSimple()
    
    # Test simple
    accelerateurs.test_simple()
    
    # Test vitesse
    accelerateurs.test_vitesse_modeles()
    
    # Benchmark complet
    accelerateurs.benchmark_complet()
    
    print("\n✅ Tests terminés")
