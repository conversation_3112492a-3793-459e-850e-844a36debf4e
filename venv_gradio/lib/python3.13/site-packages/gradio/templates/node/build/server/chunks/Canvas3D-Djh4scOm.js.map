{"version": 3, "file": "Canvas3D-Djh4scOm.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Canvas3D.js"], "sourcesContent": ["import { create_ssr_component, add_attribute } from \"svelte/internal\";\nimport { onMount } from \"svelte\";\nimport { r as resolve_wasm_src } from \"./DownloadLink.js\";\nconst Canvas3D = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let url;\n  let BABYLON_VIEWER;\n  let { value } = $$props;\n  let { display_mode } = $$props;\n  let { clear_color } = $$props;\n  let { camera_position } = $$props;\n  let { zoom_speed } = $$props;\n  let { pan_speed } = $$props;\n  let { resolved_url = void 0 } = $$props;\n  let latest_url;\n  let canvas;\n  let viewer;\n  let viewerDetails;\n  let mounted = false;\n  onMount(() => {\n    const initViewer = async () => {\n      BABYLON_VIEWER = await import(\"./index17.js\").then((n) => n.bY);\n      BABYLON_VIEWER.createViewerForCanvas(canvas, {\n        clearColor: clear_color,\n        useRightHandedSystem: true,\n        animationAutoPlay: true,\n        cameraAutoOrbit: { enabled: false },\n        onInitialized: (details) => {\n          viewerDetails = details;\n        }\n      }).then((promiseViewer) => {\n        viewer = promiseViewer;\n        mounted = true;\n      });\n    };\n    initViewer();\n    return () => {\n      viewer?.dispose();\n    };\n  });\n  function setRenderingMode(pointsCloud, wireframe) {\n    viewerDetails.scene.forcePointsCloud = pointsCloud;\n    viewerDetails.scene.forceWireframe = wireframe;\n  }\n  function load_model(url2) {\n    if (viewer) {\n      if (url2) {\n        viewer.loadModel(url2, {\n          pluginOptions: { obj: { importVertexColors: true } }\n        }).then(() => {\n          if (display_mode === \"point_cloud\") {\n            setRenderingMode(true, false);\n          } else if (display_mode === \"wireframe\") {\n            setRenderingMode(false, true);\n          } else {\n            update_camera(camera_position, zoom_speed, pan_speed);\n          }\n        });\n      } else {\n        viewer.resetModel();\n      }\n    }\n  }\n  function update_camera(camera_position2, zoom_speed2, pan_speed2) {\n    const camera = viewerDetails.camera;\n    if (camera_position2[0] !== null) {\n      camera.alpha = camera_position2[0] * Math.PI / 180;\n    }\n    if (camera_position2[1] !== null) {\n      camera.beta = camera_position2[1] * Math.PI / 180;\n    }\n    if (camera_position2[2] !== null) {\n      camera.radius = camera_position2[2];\n    }\n    camera.lowerRadiusLimit = 0.1;\n    const updateCameraSensibility = () => {\n      camera.wheelPrecision = 250 / (camera.radius * zoom_speed2);\n      camera.panningSensibility = 1e4 * pan_speed2 / camera.radius;\n    };\n    updateCameraSensibility();\n    camera.onAfterCheckInputsObservable.add(updateCameraSensibility);\n  }\n  function reset_camera_position() {\n    if (viewerDetails) {\n      viewer.resetCamera();\n    }\n  }\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.display_mode === void 0 && $$bindings.display_mode && display_mode !== void 0)\n    $$bindings.display_mode(display_mode);\n  if ($$props.clear_color === void 0 && $$bindings.clear_color && clear_color !== void 0)\n    $$bindings.clear_color(clear_color);\n  if ($$props.camera_position === void 0 && $$bindings.camera_position && camera_position !== void 0)\n    $$bindings.camera_position(camera_position);\n  if ($$props.zoom_speed === void 0 && $$bindings.zoom_speed && zoom_speed !== void 0)\n    $$bindings.zoom_speed(zoom_speed);\n  if ($$props.pan_speed === void 0 && $$bindings.pan_speed && pan_speed !== void 0)\n    $$bindings.pan_speed(pan_speed);\n  if ($$props.resolved_url === void 0 && $$bindings.resolved_url && resolved_url !== void 0)\n    $$bindings.resolved_url(resolved_url);\n  if ($$props.update_camera === void 0 && $$bindings.update_camera && update_camera !== void 0)\n    $$bindings.update_camera(update_camera);\n  if ($$props.reset_camera_position === void 0 && $$bindings.reset_camera_position && reset_camera_position !== void 0)\n    $$bindings.reset_camera_position(reset_camera_position);\n  url = value.url;\n  {\n    {\n      resolved_url = url;\n      if (url) {\n        latest_url = url;\n        const resolving_url = url;\n        resolve_wasm_src(url).then((resolved) => {\n          if (latest_url === resolving_url) {\n            resolved_url = resolved ?? void 0;\n          } else {\n            resolved && URL.revokeObjectURL(resolved);\n          }\n        });\n      }\n    }\n  }\n  mounted && load_model(resolved_url);\n  return `<canvas${add_attribute(\"this\", canvas, 0)}></canvas>`;\n});\nexport {\n  Canvas3D as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,QAAQ,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAChF,EAAE,IAAI,GAAG,CAAC;AAEV,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,YAAY,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,MAAM,CAAC;AAEb,EAAE,IAAI,aAAa,CAAC;AA8CpB,EAAE,SAAS,aAAa,CAAC,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE;AACpE,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACxC,IAAI,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AACtC,MAAM,MAAM,CAAC,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACzD,KAAK;AACL,IAAI,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AACtC,MAAM,MAAM,CAAC,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AACtC,MAAM,MAAM,CAAC,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC;AAClC,IAAI,MAAM,uBAAuB,GAAG,MAAM;AAC1C,MAAM,MAAM,CAAC,cAAc,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;AAClE,MAAM,MAAM,CAAC,kBAAkB,GAAG,GAAG,GAAG,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AACnE,KAAK,CAAC;AACN,IAAI,uBAAuB,EAAE,CAAC;AAC9B,IAAI,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AACrE,GAAG;AACH,EAAE,SAAS,qBAAqB,GAAG;AAInC,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,qBAAqB,IAAI,qBAAqB,KAAK,KAAK,CAAC;AACtH,IAAI,UAAU,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;AAC5D,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAClB,EAAE;AACF,IAAI;AACJ,MAAM,YAAY,GAAG,GAAG,CAAC;AACzB,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,UAAU,GAAG,GAAG,CAAC;AACzB,QAAQ,MAAM,aAAa,GAAG,GAAG,CAAC;AAClC,QAAQ,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AACjD,UAAU,IAAI,UAAU,KAAK,aAAa,EAAE;AAC5C,YAAY,YAAY,GAAG,QAAQ,IAAI,KAAK,CAAC,CAAC;AAC9C,WAAW,MAAM;AACjB,YAAY,QAAQ,IAAI,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACtD,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL,GAAG;AAEH,EAAE,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAChE,CAAC;;;;"}