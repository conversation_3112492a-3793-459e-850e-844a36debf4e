const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-CJ3DTY-J.js')).default;
const imports = ["_app/immutable/nodes/1.BGcyJGZ2.js","_app/immutable/chunks/stores.BS_SVy0_.js","_app/immutable/chunks/client.DUstWztb.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-DPABze_k.js.map
