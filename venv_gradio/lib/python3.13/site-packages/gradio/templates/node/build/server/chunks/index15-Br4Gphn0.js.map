{"version": 3, "file": "index15-Br4Gphn0.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index15.js"], "sourcesContent": ["import { P as Parser, N as NodeSet, a as NodeType, D as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, b as NodeP<PERSON>, T as <PERSON>, I as IterMode } from \"./Index16.js\";\nclass Stack {\n  /**\n  @internal\n  */\n  constructor(p, stack, state, reducePos, pos, score, buffer, bufferBase, curContext, lookAhead = 0, parent) {\n    this.p = p;\n    this.stack = stack;\n    this.state = state;\n    this.reducePos = reducePos;\n    this.pos = pos;\n    this.score = score;\n    this.buffer = buffer;\n    this.bufferBase = bufferBase;\n    this.curContext = curContext;\n    this.lookAhead = lookAhead;\n    this.parent = parent;\n  }\n  /**\n  @internal\n  */\n  toString() {\n    return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n  }\n  // Start an empty stack\n  /**\n  @internal\n  */\n  static start(p, state, pos = 0) {\n    let cx = p.parser.context;\n    return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n  }\n  /**\n  The stack's current [context](#lr.ContextTracker) value, if\n  any. Its type will depend on the context tracker's type\n  parameter, or it will be `null` if there is no context\n  tracker.\n  */\n  get context() {\n    return this.curContext ? this.curContext.context : null;\n  }\n  // Push a state onto the stack, tracking its start position as well\n  // as the buffer base at that point.\n  /**\n  @internal\n  */\n  pushState(state, start) {\n    this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n    this.state = state;\n  }\n  // Apply a reduce action\n  /**\n  @internal\n  */\n  reduce(action) {\n    var _a;\n    let depth = action >> 19, type = action & 65535;\n    let { parser } = this.p;\n    let dPrec = parser.dynamicPrecedence(type);\n    if (dPrec)\n      this.score += dPrec;\n    if (depth == 0) {\n      this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n      if (type < parser.minRepeatTerm)\n        this.storeNode(type, this.reducePos, this.reducePos, 4, true);\n      this.reduceContext(type, this.reducePos);\n      return;\n    }\n    let base = this.stack.length - (depth - 1) * 3 - (action & 262144 ? 6 : 0);\n    let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n    if (size >= 2e3 && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n      if (start == this.p.lastBigReductionStart) {\n        this.p.bigReductionCount++;\n        this.p.lastBigReductionSize = size;\n      } else if (this.p.lastBigReductionSize < size) {\n        this.p.bigReductionCount = 1;\n        this.p.lastBigReductionStart = start;\n        this.p.lastBigReductionSize = size;\n      }\n    }\n    let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n    if (type < parser.minRepeatTerm || action & 131072) {\n      let pos = parser.stateFlag(\n        this.state,\n        1\n        /* StateFlag.Skipped */\n      ) ? this.pos : this.reducePos;\n      this.storeNode(type, start, pos, count + 4, true);\n    }\n    if (action & 262144) {\n      this.state = this.stack[base];\n    } else {\n      let baseStateID = this.stack[base - 3];\n      this.state = parser.getGoto(baseStateID, type, true);\n    }\n    while (this.stack.length > base)\n      this.stack.pop();\n    this.reduceContext(type, start);\n  }\n  // Shift a value into the buffer\n  /**\n  @internal\n  */\n  storeNode(term, start, end, size = 4, isReduce = false) {\n    if (term == 0 && (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n      let cur = this, top = this.buffer.length;\n      if (top == 0 && cur.parent) {\n        top = cur.bufferBase - cur.parent.bufferBase;\n        cur = cur.parent;\n      }\n      if (top > 0 && cur.buffer[top - 4] == 0 && cur.buffer[top - 1] > -1) {\n        if (start == end)\n          return;\n        if (cur.buffer[top - 2] >= start) {\n          cur.buffer[top - 2] = end;\n          return;\n        }\n      }\n    }\n    if (!isReduce || this.pos == end) {\n      this.buffer.push(term, start, end, size);\n    } else {\n      let index = this.buffer.length;\n      if (index > 0 && this.buffer[index - 4] != 0)\n        while (index > 0 && this.buffer[index - 2] > end) {\n          this.buffer[index] = this.buffer[index - 4];\n          this.buffer[index + 1] = this.buffer[index - 3];\n          this.buffer[index + 2] = this.buffer[index - 2];\n          this.buffer[index + 3] = this.buffer[index - 1];\n          index -= 4;\n          if (size > 4)\n            size -= 4;\n        }\n      this.buffer[index] = term;\n      this.buffer[index + 1] = start;\n      this.buffer[index + 2] = end;\n      this.buffer[index + 3] = size;\n    }\n  }\n  // Apply a shift action\n  /**\n  @internal\n  */\n  shift(action, type, start, end) {\n    if (action & 131072) {\n      this.pushState(action & 65535, this.pos);\n    } else if ((action & 262144) == 0) {\n      let nextState = action, { parser } = this.p;\n      if (end > this.pos || type <= parser.maxNode) {\n        this.pos = end;\n        if (!parser.stateFlag(\n          nextState,\n          1\n          /* StateFlag.Skipped */\n        ))\n          this.reducePos = end;\n      }\n      this.pushState(nextState, start);\n      this.shiftContext(type, start);\n      if (type <= parser.maxNode)\n        this.buffer.push(type, start, end, 4);\n    } else {\n      this.pos = end;\n      this.shiftContext(type, start);\n      if (type <= this.p.parser.maxNode)\n        this.buffer.push(type, start, end, 4);\n    }\n  }\n  // Apply an action\n  /**\n  @internal\n  */\n  apply(action, next, nextStart, nextEnd) {\n    if (action & 65536)\n      this.reduce(action);\n    else\n      this.shift(action, next, nextStart, nextEnd);\n  }\n  // Add a prebuilt (reused) node into the buffer.\n  /**\n  @internal\n  */\n  useNode(value, next) {\n    let index = this.p.reused.length - 1;\n    if (index < 0 || this.p.reused[index] != value) {\n      this.p.reused.push(value);\n      index++;\n    }\n    let start = this.pos;\n    this.reducePos = this.pos = start + value.length;\n    this.pushState(next, start);\n    this.buffer.push(\n      index,\n      start,\n      this.reducePos,\n      -1\n      /* size == -1 means this is a reused value */\n    );\n    if (this.curContext)\n      this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n  }\n  // Split the stack. Due to the buffer sharing and the fact\n  // that `this.stack` tends to stay quite shallow, this isn't very\n  // expensive.\n  /**\n  @internal\n  */\n  split() {\n    let parent = this;\n    let off = parent.buffer.length;\n    while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n      off -= 4;\n    let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n    while (parent && base == parent.bufferBase)\n      parent = parent.parent;\n    return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n  }\n  // Try to recover from an error by 'deleting' (ignoring) one token.\n  /**\n  @internal\n  */\n  recoverByDelete(next, nextEnd) {\n    let isNode = next <= this.p.parser.maxNode;\n    if (isNode)\n      this.storeNode(next, this.pos, nextEnd, 4);\n    this.storeNode(0, this.pos, nextEnd, isNode ? 8 : 4);\n    this.pos = this.reducePos = nextEnd;\n    this.score -= 190;\n  }\n  /**\n  Check if the given term would be able to be shifted (optionally\n  after some reductions) on this stack. This can be useful for\n  external tokenizers that want to make sure they only provide a\n  given token when it applies.\n  */\n  canShift(term) {\n    for (let sim = new SimulatedStack(this); ; ) {\n      let action = this.p.parser.stateSlot(\n        sim.state,\n        4\n        /* ParseState.DefaultReduce */\n      ) || this.p.parser.hasAction(sim.state, term);\n      if (action == 0)\n        return false;\n      if ((action & 65536) == 0)\n        return true;\n      sim.reduce(action);\n    }\n  }\n  // Apply up to Recover.MaxNext recovery actions that conceptually\n  // inserts some missing token or rule.\n  /**\n  @internal\n  */\n  recoverByInsert(next) {\n    if (this.stack.length >= 300)\n      return [];\n    let nextStates = this.p.parser.nextStates(this.state);\n    if (nextStates.length > 4 << 1 || this.stack.length >= 120) {\n      let best = [];\n      for (let i = 0, s; i < nextStates.length; i += 2) {\n        if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n          best.push(nextStates[i], s);\n      }\n      if (this.stack.length < 120)\n        for (let i = 0; best.length < 4 << 1 && i < nextStates.length; i += 2) {\n          let s = nextStates[i + 1];\n          if (!best.some((v, i2) => i2 & 1 && v == s))\n            best.push(nextStates[i], s);\n        }\n      nextStates = best;\n    }\n    let result = [];\n    for (let i = 0; i < nextStates.length && result.length < 4; i += 2) {\n      let s = nextStates[i + 1];\n      if (s == this.state)\n        continue;\n      let stack = this.split();\n      stack.pushState(s, this.pos);\n      stack.storeNode(0, stack.pos, stack.pos, 4, true);\n      stack.shiftContext(nextStates[i], this.pos);\n      stack.reducePos = this.pos;\n      stack.score -= 200;\n      result.push(stack);\n    }\n    return result;\n  }\n  // Force a reduce, if possible. Return false if that can't\n  // be done.\n  /**\n  @internal\n  */\n  forceReduce() {\n    let { parser } = this.p;\n    let reduce = parser.stateSlot(\n      this.state,\n      5\n      /* ParseState.ForcedReduce */\n    );\n    if ((reduce & 65536) == 0)\n      return false;\n    if (!parser.validAction(this.state, reduce)) {\n      let depth = reduce >> 19, term = reduce & 65535;\n      let target = this.stack.length - depth * 3;\n      if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n        let backup = this.findForcedReduction();\n        if (backup == null)\n          return false;\n        reduce = backup;\n      }\n      this.storeNode(0, this.pos, this.pos, 4, true);\n      this.score -= 100;\n    }\n    this.reducePos = this.pos;\n    this.reduce(reduce);\n    return true;\n  }\n  /**\n  Try to scan through the automaton to find some kind of reduction\n  that can be applied. Used when the regular ForcedReduce field\n  isn't a valid action. @internal\n  */\n  findForcedReduction() {\n    let { parser } = this.p, seen = [];\n    let explore = (state, depth) => {\n      if (seen.includes(state))\n        return;\n      seen.push(state);\n      return parser.allActions(state, (action) => {\n        if (action & (262144 | 131072))\n          ;\n        else if (action & 65536) {\n          let rDepth = (action >> 19) - depth;\n          if (rDepth > 1) {\n            let term = action & 65535, target = this.stack.length - rDepth * 3;\n            if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n              return rDepth << 19 | 65536 | term;\n          }\n        } else {\n          let found = explore(action, depth + 1);\n          if (found != null)\n            return found;\n        }\n      });\n    };\n    return explore(this.state, 0);\n  }\n  /**\n  @internal\n  */\n  forceAll() {\n    while (!this.p.parser.stateFlag(\n      this.state,\n      2\n      /* StateFlag.Accepting */\n    )) {\n      if (!this.forceReduce()) {\n        this.storeNode(0, this.pos, this.pos, 4, true);\n        break;\n      }\n    }\n    return this;\n  }\n  /**\n  Check whether this state has no further actions (assumed to be a direct descendant of the\n  top state, since any other states must be able to continue\n  somehow). @internal\n  */\n  get deadEnd() {\n    if (this.stack.length != 3)\n      return false;\n    let { parser } = this.p;\n    return parser.data[parser.stateSlot(\n      this.state,\n      1\n      /* ParseState.Actions */\n    )] == 65535 && !parser.stateSlot(\n      this.state,\n      4\n      /* ParseState.DefaultReduce */\n    );\n  }\n  /**\n  Restart the stack (put it back in its start state). Only safe\n  when this.stack.length == 3 (state is directly below the top\n  state). @internal\n  */\n  restart() {\n    this.storeNode(0, this.pos, this.pos, 4, true);\n    this.state = this.stack[0];\n    this.stack.length = 0;\n  }\n  /**\n  @internal\n  */\n  sameState(other) {\n    if (this.state != other.state || this.stack.length != other.stack.length)\n      return false;\n    for (let i = 0; i < this.stack.length; i += 3)\n      if (this.stack[i] != other.stack[i])\n        return false;\n    return true;\n  }\n  /**\n  Get the parser used by this stack.\n  */\n  get parser() {\n    return this.p.parser;\n  }\n  /**\n  Test whether a given dialect (by numeric ID, as exported from\n  the terms file) is enabled.\n  */\n  dialectEnabled(dialectID) {\n    return this.p.parser.dialect.flags[dialectID];\n  }\n  shiftContext(term, start) {\n    if (this.curContext)\n      this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n  }\n  reduceContext(term, start) {\n    if (this.curContext)\n      this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n  }\n  /**\n  @internal\n  */\n  emitContext() {\n    let last = this.buffer.length - 1;\n    if (last < 0 || this.buffer[last] != -3)\n      this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n  }\n  /**\n  @internal\n  */\n  emitLookAhead() {\n    let last = this.buffer.length - 1;\n    if (last < 0 || this.buffer[last] != -4)\n      this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n  }\n  updateContext(context) {\n    if (context != this.curContext.context) {\n      let newCx = new StackContext(this.curContext.tracker, context);\n      if (newCx.hash != this.curContext.hash)\n        this.emitContext();\n      this.curContext = newCx;\n    }\n  }\n  /**\n  @internal\n  */\n  setLookAhead(lookAhead) {\n    if (lookAhead > this.lookAhead) {\n      this.emitLookAhead();\n      this.lookAhead = lookAhead;\n    }\n  }\n  /**\n  @internal\n  */\n  close() {\n    if (this.curContext && this.curContext.tracker.strict)\n      this.emitContext();\n    if (this.lookAhead > 0)\n      this.emitLookAhead();\n  }\n}\nclass StackContext {\n  constructor(tracker, context) {\n    this.tracker = tracker;\n    this.context = context;\n    this.hash = tracker.strict ? tracker.hash(context) : 0;\n  }\n}\nclass SimulatedStack {\n  constructor(start) {\n    this.start = start;\n    this.state = start.state;\n    this.stack = start.stack;\n    this.base = this.stack.length;\n  }\n  reduce(action) {\n    let term = action & 65535, depth = action >> 19;\n    if (depth == 0) {\n      if (this.stack == this.start.stack)\n        this.stack = this.stack.slice();\n      this.stack.push(this.state, 0, 0);\n      this.base += 3;\n    } else {\n      this.base -= (depth - 1) * 3;\n    }\n    let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n    this.state = goto;\n  }\n}\nclass StackBufferCursor {\n  constructor(stack, pos, index) {\n    this.stack = stack;\n    this.pos = pos;\n    this.index = index;\n    this.buffer = stack.buffer;\n    if (this.index == 0)\n      this.maybeNext();\n  }\n  static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n    return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n  }\n  maybeNext() {\n    let next = this.stack.parent;\n    if (next != null) {\n      this.index = this.stack.bufferBase - next.bufferBase;\n      this.stack = next;\n      this.buffer = next.buffer;\n    }\n  }\n  get id() {\n    return this.buffer[this.index - 4];\n  }\n  get start() {\n    return this.buffer[this.index - 3];\n  }\n  get end() {\n    return this.buffer[this.index - 2];\n  }\n  get size() {\n    return this.buffer[this.index - 1];\n  }\n  next() {\n    this.index -= 4;\n    this.pos -= 4;\n    if (this.index == 0)\n      this.maybeNext();\n  }\n  fork() {\n    return new StackBufferCursor(this.stack, this.pos, this.index);\n  }\n}\nfunction decodeArray(input, Type = Uint16Array) {\n  if (typeof input != \"string\")\n    return input;\n  let array = null;\n  for (let pos = 0, out = 0; pos < input.length; ) {\n    let value = 0;\n    for (; ; ) {\n      let next = input.charCodeAt(pos++), stop = false;\n      if (next == 126) {\n        value = 65535;\n        break;\n      }\n      if (next >= 92)\n        next--;\n      if (next >= 34)\n        next--;\n      let digit = next - 32;\n      if (digit >= 46) {\n        digit -= 46;\n        stop = true;\n      }\n      value += digit;\n      if (stop)\n        break;\n      value *= 46;\n    }\n    if (array)\n      array[out++] = value;\n    else\n      array = new Type(value);\n  }\n  return array;\n}\nclass CachedToken {\n  constructor() {\n    this.start = -1;\n    this.value = -1;\n    this.end = -1;\n    this.extended = -1;\n    this.lookAhead = 0;\n    this.mask = 0;\n    this.context = 0;\n  }\n}\nconst nullToken = new CachedToken();\nclass InputStream {\n  /**\n  @internal\n  */\n  constructor(input, ranges) {\n    this.input = input;\n    this.ranges = ranges;\n    this.chunk = \"\";\n    this.chunkOff = 0;\n    this.chunk2 = \"\";\n    this.chunk2Pos = 0;\n    this.next = -1;\n    this.token = nullToken;\n    this.rangeIndex = 0;\n    this.pos = this.chunkPos = ranges[0].from;\n    this.range = ranges[0];\n    this.end = ranges[ranges.length - 1].to;\n    this.readNext();\n  }\n  /**\n  @internal\n  */\n  resolveOffset(offset, assoc) {\n    let range = this.range, index = this.rangeIndex;\n    let pos = this.pos + offset;\n    while (pos < range.from) {\n      if (!index)\n        return null;\n      let next = this.ranges[--index];\n      pos -= range.from - next.to;\n      range = next;\n    }\n    while (assoc < 0 ? pos > range.to : pos >= range.to) {\n      if (index == this.ranges.length - 1)\n        return null;\n      let next = this.ranges[++index];\n      pos += next.from - range.to;\n      range = next;\n    }\n    return pos;\n  }\n  /**\n  @internal\n  */\n  clipPos(pos) {\n    if (pos >= this.range.from && pos < this.range.to)\n      return pos;\n    for (let range of this.ranges)\n      if (range.to > pos)\n        return Math.max(pos, range.from);\n    return this.end;\n  }\n  /**\n  Look at a code unit near the stream position. `.peek(0)` equals\n  `.next`, `.peek(-1)` gives you the previous character, and so\n  on.\n  \n  Note that looking around during tokenizing creates dependencies\n  on potentially far-away content, which may reduce the\n  effectiveness incremental parsing—when looking forward—or even\n  cause invalid reparses when looking backward more than 25 code\n  units, since the library does not track lookbehind.\n  */\n  peek(offset) {\n    let idx = this.chunkOff + offset, pos, result;\n    if (idx >= 0 && idx < this.chunk.length) {\n      pos = this.pos + offset;\n      result = this.chunk.charCodeAt(idx);\n    } else {\n      let resolved = this.resolveOffset(offset, 1);\n      if (resolved == null)\n        return -1;\n      pos = resolved;\n      if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n        result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n      } else {\n        let i = this.rangeIndex, range = this.range;\n        while (range.to <= pos)\n          range = this.ranges[++i];\n        this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n        if (pos + this.chunk2.length > range.to)\n          this.chunk2 = this.chunk2.slice(0, range.to - pos);\n        result = this.chunk2.charCodeAt(0);\n      }\n    }\n    if (pos >= this.token.lookAhead)\n      this.token.lookAhead = pos + 1;\n    return result;\n  }\n  /**\n  Accept a token. By default, the end of the token is set to the\n  current stream position, but you can pass an offset (relative to\n  the stream position) to change that.\n  */\n  acceptToken(token, endOffset = 0) {\n    let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n    if (end == null || end < this.token.start)\n      throw new RangeError(\"Token end out of bounds\");\n    this.token.value = token;\n    this.token.end = end;\n  }\n  getChunk() {\n    if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n      let { chunk, chunkPos } = this;\n      this.chunk = this.chunk2;\n      this.chunkPos = this.chunk2Pos;\n      this.chunk2 = chunk;\n      this.chunk2Pos = chunkPos;\n      this.chunkOff = this.pos - this.chunkPos;\n    } else {\n      this.chunk2 = this.chunk;\n      this.chunk2Pos = this.chunkPos;\n      let nextChunk = this.input.chunk(this.pos);\n      let end = this.pos + nextChunk.length;\n      this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n      this.chunkPos = this.pos;\n      this.chunkOff = 0;\n    }\n  }\n  readNext() {\n    if (this.chunkOff >= this.chunk.length) {\n      this.getChunk();\n      if (this.chunkOff == this.chunk.length)\n        return this.next = -1;\n    }\n    return this.next = this.chunk.charCodeAt(this.chunkOff);\n  }\n  /**\n  Move the stream forward N (defaults to 1) code units. Returns\n  the new value of [`next`](#lr.InputStream.next).\n  */\n  advance(n = 1) {\n    this.chunkOff += n;\n    while (this.pos + n >= this.range.to) {\n      if (this.rangeIndex == this.ranges.length - 1)\n        return this.setDone();\n      n -= this.range.to - this.pos;\n      this.range = this.ranges[++this.rangeIndex];\n      this.pos = this.range.from;\n    }\n    this.pos += n;\n    if (this.pos >= this.token.lookAhead)\n      this.token.lookAhead = this.pos + 1;\n    return this.readNext();\n  }\n  setDone() {\n    this.pos = this.chunkPos = this.end;\n    this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n    this.chunk = \"\";\n    return this.next = -1;\n  }\n  /**\n  @internal\n  */\n  reset(pos, token) {\n    if (token) {\n      this.token = token;\n      token.start = pos;\n      token.lookAhead = pos + 1;\n      token.value = token.extended = -1;\n    } else {\n      this.token = nullToken;\n    }\n    if (this.pos != pos) {\n      this.pos = pos;\n      if (pos == this.end) {\n        this.setDone();\n        return this;\n      }\n      while (pos < this.range.from)\n        this.range = this.ranges[--this.rangeIndex];\n      while (pos >= this.range.to)\n        this.range = this.ranges[++this.rangeIndex];\n      if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n        this.chunkOff = pos - this.chunkPos;\n      } else {\n        this.chunk = \"\";\n        this.chunkOff = 0;\n      }\n      this.readNext();\n    }\n    return this;\n  }\n  /**\n  @internal\n  */\n  read(from, to) {\n    if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n      return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n    if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n      return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n    if (from >= this.range.from && to <= this.range.to)\n      return this.input.read(from, to);\n    let result = \"\";\n    for (let r of this.ranges) {\n      if (r.from >= to)\n        break;\n      if (r.to > from)\n        result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n    }\n    return result;\n  }\n}\nclass TokenGroup {\n  constructor(data, id2) {\n    this.data = data;\n    this.id = id2;\n  }\n  token(input, stack) {\n    let { parser } = stack.p;\n    readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n  }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\nclass LocalTokenGroup {\n  constructor(data, precTable, elseToken) {\n    this.precTable = precTable;\n    this.elseToken = elseToken;\n    this.data = typeof data == \"string\" ? decodeArray(data) : data;\n  }\n  token(input, stack) {\n    let start = input.pos, skipped = 0;\n    for (; ; ) {\n      let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n      readToken(this.data, input, stack, 0, this.data, this.precTable);\n      if (input.token.value > -1)\n        break;\n      if (this.elseToken == null)\n        return;\n      if (!atEof)\n        skipped++;\n      if (nextPos == null)\n        break;\n      input.reset(nextPos, input.token);\n    }\n    if (skipped) {\n      input.reset(start, input.token);\n      input.acceptToken(this.elseToken, skipped);\n    }\n  }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\nclass ExternalTokenizer {\n  /**\n  Create a tokenizer. The first argument is the function that,\n  given an input stream, scans for the types of tokens it\n  recognizes at the stream's position, and calls\n  [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n  one.\n  */\n  constructor(token, options = {}) {\n    this.token = token;\n    this.contextual = !!options.contextual;\n    this.fallback = !!options.fallback;\n    this.extend = !!options.extend;\n  }\n}\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n  let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n  scan:\n    for (; ; ) {\n      if ((groupMask & data[state]) == 0)\n        break;\n      let accEnd = data[state + 1];\n      for (let i = state + 3; i < accEnd; i += 2)\n        if ((data[i + 1] & groupMask) > 0) {\n          let term = data[i];\n          if (dialect.allows(term) && (input.token.value == -1 || input.token.value == term || overrides(term, input.token.value, precTable, precOffset))) {\n            input.acceptToken(term);\n            break;\n          }\n        }\n      let next = input.next, low = 0, high = data[state + 2];\n      if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535) {\n        state = data[accEnd + high * 3 - 1];\n        continue scan;\n      }\n      for (; low < high; ) {\n        let mid = low + high >> 1;\n        let index = accEnd + mid + (mid << 1);\n        let from = data[index], to = data[index + 1] || 65536;\n        if (next < from)\n          high = mid;\n        else if (next >= to)\n          low = mid + 1;\n        else {\n          state = data[index + 2];\n          input.advance();\n          continue scan;\n        }\n      }\n      break;\n    }\n}\nfunction findOffset(data, start, term) {\n  for (let i = start, next; (next = data[i]) != 65535; i++)\n    if (next == term)\n      return i - start;\n  return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n  let iPrev = findOffset(tableData, tableOffset, prev);\n  return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n  let cursor = tree.cursor(IterMode.IncludeAnonymous);\n  cursor.moveTo(pos);\n  for (; ; ) {\n    if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n      for (; ; ) {\n        if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n          return side < 0 ? Math.max(0, Math.min(\n            cursor.to - 1,\n            pos - 25\n            /* Safety.Margin */\n          )) : Math.min(tree.length, Math.max(\n            cursor.from + 1,\n            pos + 25\n            /* Safety.Margin */\n          ));\n        if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n          break;\n        if (!cursor.parent())\n          return side < 0 ? 0 : tree.length;\n      }\n  }\n}\nclass FragmentCursor {\n  constructor(fragments, nodeSet) {\n    this.fragments = fragments;\n    this.nodeSet = nodeSet;\n    this.i = 0;\n    this.fragment = null;\n    this.safeFrom = -1;\n    this.safeTo = -1;\n    this.trees = [];\n    this.start = [];\n    this.index = [];\n    this.nextFragment();\n  }\n  nextFragment() {\n    let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n    if (fr) {\n      this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n      this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n      while (this.trees.length) {\n        this.trees.pop();\n        this.start.pop();\n        this.index.pop();\n      }\n      this.trees.push(fr.tree);\n      this.start.push(-fr.offset);\n      this.index.push(0);\n      this.nextStart = this.safeFrom;\n    } else {\n      this.nextStart = 1e9;\n    }\n  }\n  // `pos` must be >= any previously given `pos` for this cursor\n  nodeAt(pos) {\n    if (pos < this.nextStart)\n      return null;\n    while (this.fragment && this.safeTo <= pos)\n      this.nextFragment();\n    if (!this.fragment)\n      return null;\n    for (; ; ) {\n      let last = this.trees.length - 1;\n      if (last < 0) {\n        this.nextFragment();\n        return null;\n      }\n      let top = this.trees[last], index = this.index[last];\n      if (index == top.children.length) {\n        this.trees.pop();\n        this.start.pop();\n        this.index.pop();\n        continue;\n      }\n      let next = top.children[index];\n      let start = this.start[last] + top.positions[index];\n      if (start > pos) {\n        this.nextStart = start;\n        return null;\n      }\n      if (next instanceof Tree) {\n        if (start == pos) {\n          if (start < this.safeFrom)\n            return null;\n          let end = start + next.length;\n          if (end <= this.safeTo) {\n            let lookAhead = next.prop(NodeProp.lookAhead);\n            if (!lookAhead || end + lookAhead < this.fragment.to)\n              return next;\n          }\n        }\n        this.index[last]++;\n        if (start + next.length >= Math.max(this.safeFrom, pos)) {\n          this.trees.push(next);\n          this.start.push(start);\n          this.index.push(0);\n        }\n      } else {\n        this.index[last]++;\n        this.nextStart = start + next.length;\n      }\n    }\n  }\n}\nclass TokenCache {\n  constructor(parser, stream) {\n    this.stream = stream;\n    this.tokens = [];\n    this.mainToken = null;\n    this.actions = [];\n    this.tokens = parser.tokenizers.map((_) => new CachedToken());\n  }\n  getActions(stack) {\n    let actionIndex = 0;\n    let main = null;\n    let { parser } = stack.p, { tokenizers } = parser;\n    let mask = parser.stateSlot(\n      stack.state,\n      3\n      /* ParseState.TokenizerMask */\n    );\n    let context = stack.curContext ? stack.curContext.hash : 0;\n    let lookAhead = 0;\n    for (let i = 0; i < tokenizers.length; i++) {\n      if ((1 << i & mask) == 0)\n        continue;\n      let tokenizer = tokenizers[i], token = this.tokens[i];\n      if (main && !tokenizer.fallback)\n        continue;\n      if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n        this.updateCachedToken(token, tokenizer, stack);\n        token.mask = mask;\n        token.context = context;\n      }\n      if (token.lookAhead > token.end + 25)\n        lookAhead = Math.max(token.lookAhead, lookAhead);\n      if (token.value != 0) {\n        let startIndex = actionIndex;\n        if (token.extended > -1)\n          actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n        actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n        if (!tokenizer.extend) {\n          main = token;\n          if (actionIndex > startIndex)\n            break;\n        }\n      }\n    }\n    while (this.actions.length > actionIndex)\n      this.actions.pop();\n    if (lookAhead)\n      stack.setLookAhead(lookAhead);\n    if (!main && stack.pos == this.stream.end) {\n      main = new CachedToken();\n      main.value = stack.p.parser.eofTerm;\n      main.start = main.end = stack.pos;\n      actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n    }\n    this.mainToken = main;\n    return this.actions;\n  }\n  getMainToken(stack) {\n    if (this.mainToken)\n      return this.mainToken;\n    let main = new CachedToken(), { pos, p } = stack;\n    main.start = pos;\n    main.end = Math.min(pos + 1, p.stream.end);\n    main.value = pos == p.stream.end ? p.parser.eofTerm : 0;\n    return main;\n  }\n  updateCachedToken(token, tokenizer, stack) {\n    let start = this.stream.clipPos(stack.pos);\n    tokenizer.token(this.stream.reset(start, token), stack);\n    if (token.value > -1) {\n      let { parser } = stack.p;\n      for (let i = 0; i < parser.specialized.length; i++)\n        if (parser.specialized[i] == token.value) {\n          let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n          if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n            if ((result & 1) == 0)\n              token.value = result >> 1;\n            else\n              token.extended = result >> 1;\n            break;\n          }\n        }\n    } else {\n      token.value = 0;\n      token.end = this.stream.clipPos(start + 1);\n    }\n  }\n  putAction(action, token, end, index) {\n    for (let i = 0; i < index; i += 3)\n      if (this.actions[i] == action)\n        return index;\n    this.actions[index++] = action;\n    this.actions[index++] = token;\n    this.actions[index++] = end;\n    return index;\n  }\n  addActions(stack, token, end, index) {\n    let { state } = stack, { parser } = stack.p, { data } = parser;\n    for (let set = 0; set < 2; set++) {\n      for (let i = parser.stateSlot(\n        state,\n        set ? 2 : 1\n        /* ParseState.Actions */\n      ); ; i += 3) {\n        if (data[i] == 65535) {\n          if (data[i + 1] == 1) {\n            i = pair(data, i + 2);\n          } else {\n            if (index == 0 && data[i + 1] == 2)\n              index = this.putAction(pair(data, i + 2), token, end, index);\n            break;\n          }\n        }\n        if (data[i] == token)\n          index = this.putAction(pair(data, i + 1), token, end, index);\n      }\n    }\n    return index;\n  }\n}\nclass Parse {\n  constructor(parser, input, fragments, ranges) {\n    this.parser = parser;\n    this.input = input;\n    this.ranges = ranges;\n    this.recovering = 0;\n    this.nextStackID = 9812;\n    this.minStackPos = 0;\n    this.reused = [];\n    this.stoppedAt = null;\n    this.lastBigReductionStart = -1;\n    this.lastBigReductionSize = 0;\n    this.bigReductionCount = 0;\n    this.stream = new InputStream(input, ranges);\n    this.tokens = new TokenCache(parser, this.stream);\n    this.topTerm = parser.top[1];\n    let { from } = ranges[0];\n    this.stacks = [Stack.start(this, parser.top[0], from)];\n    this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4 ? new FragmentCursor(fragments, parser.nodeSet) : null;\n  }\n  get parsedPos() {\n    return this.minStackPos;\n  }\n  // Move the parser forward. This will process all parse stacks at\n  // `this.pos` and try to advance them to a further position. If no\n  // stack for such a position is found, it'll start error-recovery.\n  //\n  // When the parse is finished, this will return a syntax tree. When\n  // not, it returns `null`.\n  advance() {\n    let stacks = this.stacks, pos = this.minStackPos;\n    let newStacks = this.stacks = [];\n    let stopped, stoppedTokens;\n    if (this.bigReductionCount > 300 && stacks.length == 1) {\n      let [s] = stacks;\n      while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) {\n      }\n      this.bigReductionCount = this.lastBigReductionSize = 0;\n    }\n    for (let i = 0; i < stacks.length; i++) {\n      let stack = stacks[i];\n      for (; ; ) {\n        this.tokens.mainToken = null;\n        if (stack.pos > pos) {\n          newStacks.push(stack);\n        } else if (this.advanceStack(stack, newStacks, stacks)) {\n          continue;\n        } else {\n          if (!stopped) {\n            stopped = [];\n            stoppedTokens = [];\n          }\n          stopped.push(stack);\n          let tok = this.tokens.getMainToken(stack);\n          stoppedTokens.push(tok.value, tok.end);\n        }\n        break;\n      }\n    }\n    if (!newStacks.length) {\n      let finished = stopped && findFinished(stopped);\n      if (finished) {\n        if (verbose)\n          console.log(\"Finish with \" + this.stackID(finished));\n        return this.stackToTree(finished);\n      }\n      if (this.parser.strict) {\n        if (verbose && stopped)\n          console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n        throw new SyntaxError(\"No parse at \" + pos);\n      }\n      if (!this.recovering)\n        this.recovering = 5;\n    }\n    if (this.recovering && stopped) {\n      let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0] : this.runRecovery(stopped, stoppedTokens, newStacks);\n      if (finished) {\n        if (verbose)\n          console.log(\"Force-finish \" + this.stackID(finished));\n        return this.stackToTree(finished.forceAll());\n      }\n    }\n    if (this.recovering) {\n      let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3;\n      if (newStacks.length > maxRemaining) {\n        newStacks.sort((a, b) => b.score - a.score);\n        while (newStacks.length > maxRemaining)\n          newStacks.pop();\n      }\n      if (newStacks.some((s) => s.reducePos > pos))\n        this.recovering--;\n    } else if (newStacks.length > 1) {\n      outer:\n        for (let i = 0; i < newStacks.length - 1; i++) {\n          let stack = newStacks[i];\n          for (let j = i + 1; j < newStacks.length; j++) {\n            let other = newStacks[j];\n            if (stack.sameState(other) || stack.buffer.length > 500 && other.buffer.length > 500) {\n              if ((stack.score - other.score || stack.buffer.length - other.buffer.length) > 0) {\n                newStacks.splice(j--, 1);\n              } else {\n                newStacks.splice(i--, 1);\n                continue outer;\n              }\n            }\n          }\n        }\n      if (newStacks.length > 12)\n        newStacks.splice(\n          12,\n          newStacks.length - 12\n          /* Rec.MaxStackCount */\n        );\n    }\n    this.minStackPos = newStacks[0].pos;\n    for (let i = 1; i < newStacks.length; i++)\n      if (newStacks[i].pos < this.minStackPos)\n        this.minStackPos = newStacks[i].pos;\n    return null;\n  }\n  stopAt(pos) {\n    if (this.stoppedAt != null && this.stoppedAt < pos)\n      throw new RangeError(\"Can't move stoppedAt forward\");\n    this.stoppedAt = pos;\n  }\n  // Returns an updated version of the given stack, or null if the\n  // stack can't advance normally. When `split` and `stacks` are\n  // given, stacks split off by ambiguous operations will be pushed to\n  // `split`, or added to `stacks` if they move `pos` forward.\n  advanceStack(stack, stacks, split) {\n    let start = stack.pos, { parser } = this;\n    let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n    if (this.stoppedAt != null && start > this.stoppedAt)\n      return stack.forceReduce() ? stack : null;\n    if (this.fragments) {\n      let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n      for (let cached = this.fragments.nodeAt(start); cached; ) {\n        let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n        if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n          stack.useNode(cached, match);\n          if (verbose)\n            console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n          return true;\n        }\n        if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n          break;\n        let inner = cached.children[0];\n        if (inner instanceof Tree && cached.positions[0] == 0)\n          cached = inner;\n        else\n          break;\n      }\n    }\n    let defaultReduce = parser.stateSlot(\n      stack.state,\n      4\n      /* ParseState.DefaultReduce */\n    );\n    if (defaultReduce > 0) {\n      stack.reduce(defaultReduce);\n      if (verbose)\n        console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(\n          defaultReduce & 65535\n          /* Action.ValueMask */\n        )})`);\n      return true;\n    }\n    if (stack.stack.length >= 8400) {\n      while (stack.stack.length > 6e3 && stack.forceReduce()) {\n      }\n    }\n    let actions = this.tokens.getActions(stack);\n    for (let i = 0; i < actions.length; ) {\n      let action = actions[i++], term = actions[i++], end = actions[i++];\n      let last = i == actions.length || !split;\n      let localStack = last ? stack : stack.split();\n      let main = this.tokens.mainToken;\n      localStack.apply(action, term, main ? main.start : localStack.pos, end);\n      if (verbose)\n        console.log(base + this.stackID(localStack) + ` (via ${(action & 65536) == 0 ? \"shift\" : `reduce of ${parser.getName(\n          action & 65535\n          /* Action.ValueMask */\n        )}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n      if (last)\n        return true;\n      else if (localStack.pos > start)\n        stacks.push(localStack);\n      else\n        split.push(localStack);\n    }\n    return false;\n  }\n  // Advance a given stack forward as far as it will go. Returns the\n  // (possibly updated) stack if it got stuck, or null if it moved\n  // forward and was given to `pushStackDedup`.\n  advanceFully(stack, newStacks) {\n    let pos = stack.pos;\n    for (; ; ) {\n      if (!this.advanceStack(stack, null, null))\n        return false;\n      if (stack.pos > pos) {\n        pushStackDedup(stack, newStacks);\n        return true;\n      }\n    }\n  }\n  runRecovery(stacks, tokens, newStacks) {\n    let finished = null, restarted = false;\n    for (let i = 0; i < stacks.length; i++) {\n      let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n      let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n      if (stack.deadEnd) {\n        if (restarted)\n          continue;\n        restarted = true;\n        stack.restart();\n        if (verbose)\n          console.log(base + this.stackID(stack) + \" (restarted)\");\n        let done = this.advanceFully(stack, newStacks);\n        if (done)\n          continue;\n      }\n      let force = stack.split(), forceBase = base;\n      for (let j = 0; force.forceReduce() && j < 10; j++) {\n        if (verbose)\n          console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n        let done = this.advanceFully(force, newStacks);\n        if (done)\n          break;\n        if (verbose)\n          forceBase = this.stackID(force) + \" -> \";\n      }\n      for (let insert of stack.recoverByInsert(token)) {\n        if (verbose)\n          console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n        this.advanceFully(insert, newStacks);\n      }\n      if (this.stream.end > stack.pos) {\n        if (tokenEnd == stack.pos) {\n          tokenEnd++;\n          token = 0;\n        }\n        stack.recoverByDelete(token, tokenEnd);\n        if (verbose)\n          console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n        pushStackDedup(stack, newStacks);\n      } else if (!finished || finished.score < stack.score) {\n        finished = stack;\n      }\n    }\n    return finished;\n  }\n  // Convert the stack's buffer to a syntax tree.\n  stackToTree(stack) {\n    stack.close();\n    return Tree.build({\n      buffer: StackBufferCursor.create(stack),\n      nodeSet: this.parser.nodeSet,\n      topID: this.topTerm,\n      maxBufferLength: this.parser.bufferLength,\n      reused: this.reused,\n      start: this.ranges[0].from,\n      length: stack.pos - this.ranges[0].from,\n      minRepeatType: this.parser.minRepeatTerm\n    });\n  }\n  stackID(stack) {\n    let id2 = (stackIDs || (stackIDs = /* @__PURE__ */ new WeakMap())).get(stack);\n    if (!id2)\n      stackIDs.set(stack, id2 = String.fromCodePoint(this.nextStackID++));\n    return id2 + stack;\n  }\n}\nfunction pushStackDedup(stack, newStacks) {\n  for (let i = 0; i < newStacks.length; i++) {\n    let other = newStacks[i];\n    if (other.pos == stack.pos && other.sameState(stack)) {\n      if (newStacks[i].score < stack.score)\n        newStacks[i] = stack;\n      return;\n    }\n  }\n  newStacks.push(stack);\n}\nclass Dialect {\n  constructor(source, flags, disabled) {\n    this.source = source;\n    this.flags = flags;\n    this.disabled = disabled;\n  }\n  allows(term) {\n    return !this.disabled || this.disabled[term] == 0;\n  }\n}\nconst id = (x) => x;\nclass ContextTracker {\n  /**\n  Define a context tracker.\n  */\n  constructor(spec) {\n    this.start = spec.start;\n    this.shift = spec.shift || id;\n    this.reduce = spec.reduce || id;\n    this.reuse = spec.reuse || id;\n    this.hash = spec.hash || (() => 0);\n    this.strict = spec.strict !== false;\n  }\n}\nclass LRParser extends Parser {\n  /**\n  @internal\n  */\n  constructor(spec) {\n    super();\n    this.wrappers = [];\n    if (spec.version != 14)\n      throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14})`);\n    let nodeNames = spec.nodeNames.split(\" \");\n    this.minRepeatTerm = nodeNames.length;\n    for (let i = 0; i < spec.repeatNodeCount; i++)\n      nodeNames.push(\"\");\n    let topTerms = Object.keys(spec.topRules).map((r) => spec.topRules[r][1]);\n    let nodeProps = [];\n    for (let i = 0; i < nodeNames.length; i++)\n      nodeProps.push([]);\n    function setProp(nodeID, prop, value) {\n      nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n    }\n    if (spec.nodeProps)\n      for (let propSpec of spec.nodeProps) {\n        let prop = propSpec[0];\n        if (typeof prop == \"string\")\n          prop = NodeProp[prop];\n        for (let i = 1; i < propSpec.length; ) {\n          let next = propSpec[i++];\n          if (next >= 0) {\n            setProp(next, prop, propSpec[i++]);\n          } else {\n            let value = propSpec[i + -next];\n            for (let j = -next; j > 0; j--)\n              setProp(propSpec[i++], prop, value);\n            i++;\n          }\n        }\n      }\n    this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n      name: i >= this.minRepeatTerm ? void 0 : name,\n      id: i,\n      props: nodeProps[i],\n      top: topTerms.indexOf(i) > -1,\n      error: i == 0,\n      skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n    })));\n    if (spec.propSources)\n      this.nodeSet = this.nodeSet.extend(...spec.propSources);\n    this.strict = false;\n    this.bufferLength = DefaultBufferLength;\n    let tokenArray = decodeArray(spec.tokenData);\n    this.context = spec.context;\n    this.specializerSpecs = spec.specialized || [];\n    this.specialized = new Uint16Array(this.specializerSpecs.length);\n    for (let i = 0; i < this.specializerSpecs.length; i++)\n      this.specialized[i] = this.specializerSpecs[i].term;\n    this.specializers = this.specializerSpecs.map(getSpecializer);\n    this.states = decodeArray(spec.states, Uint32Array);\n    this.data = decodeArray(spec.stateData);\n    this.goto = decodeArray(spec.goto);\n    this.maxTerm = spec.maxTerm;\n    this.tokenizers = spec.tokenizers.map((value) => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n    this.topRules = spec.topRules;\n    this.dialects = spec.dialects || {};\n    this.dynamicPrecedences = spec.dynamicPrecedences || null;\n    this.tokenPrecTable = spec.tokenPrec;\n    this.termNames = spec.termNames || null;\n    this.maxNode = this.nodeSet.types.length - 1;\n    this.dialect = this.parseDialect();\n    this.top = this.topRules[Object.keys(this.topRules)[0]];\n  }\n  createParse(input, fragments, ranges) {\n    let parse = new Parse(this, input, fragments, ranges);\n    for (let w of this.wrappers)\n      parse = w(parse, input, fragments, ranges);\n    return parse;\n  }\n  /**\n  Get a goto table entry @internal\n  */\n  getGoto(state, term, loose = false) {\n    let table = this.goto;\n    if (term >= table[0])\n      return -1;\n    for (let pos = table[term + 1]; ; ) {\n      let groupTag = table[pos++], last = groupTag & 1;\n      let target = table[pos++];\n      if (last && loose)\n        return target;\n      for (let end = pos + (groupTag >> 1); pos < end; pos++)\n        if (table[pos] == state)\n          return target;\n      if (last)\n        return -1;\n    }\n  }\n  /**\n  Check if this state has an action for a given terminal @internal\n  */\n  hasAction(state, terminal) {\n    let data = this.data;\n    for (let set = 0; set < 2; set++) {\n      for (let i = this.stateSlot(\n        state,\n        set ? 2 : 1\n        /* ParseState.Actions */\n      ), next; ; i += 3) {\n        if ((next = data[i]) == 65535) {\n          if (data[i + 1] == 1)\n            next = data[i = pair(data, i + 2)];\n          else if (data[i + 1] == 2)\n            return pair(data, i + 2);\n          else\n            break;\n        }\n        if (next == terminal || next == 0)\n          return pair(data, i + 1);\n      }\n    }\n    return 0;\n  }\n  /**\n  @internal\n  */\n  stateSlot(state, slot) {\n    return this.states[state * 6 + slot];\n  }\n  /**\n  @internal\n  */\n  stateFlag(state, flag) {\n    return (this.stateSlot(\n      state,\n      0\n      /* ParseState.Flags */\n    ) & flag) > 0;\n  }\n  /**\n  @internal\n  */\n  validAction(state, action) {\n    return !!this.allActions(state, (a) => a == action ? true : null);\n  }\n  /**\n  @internal\n  */\n  allActions(state, action) {\n    let deflt = this.stateSlot(\n      state,\n      4\n      /* ParseState.DefaultReduce */\n    );\n    let result = deflt ? action(deflt) : void 0;\n    for (let i = this.stateSlot(\n      state,\n      1\n      /* ParseState.Actions */\n    ); result == null; i += 3) {\n      if (this.data[i] == 65535) {\n        if (this.data[i + 1] == 1)\n          i = pair(this.data, i + 2);\n        else\n          break;\n      }\n      result = action(pair(this.data, i + 1));\n    }\n    return result;\n  }\n  /**\n  Get the states that can follow this one through shift actions or\n  goto jumps. @internal\n  */\n  nextStates(state) {\n    let result = [];\n    for (let i = this.stateSlot(\n      state,\n      1\n      /* ParseState.Actions */\n    ); ; i += 3) {\n      if (this.data[i] == 65535) {\n        if (this.data[i + 1] == 1)\n          i = pair(this.data, i + 2);\n        else\n          break;\n      }\n      if ((this.data[i + 2] & 65536 >> 16) == 0) {\n        let value = this.data[i + 1];\n        if (!result.some((v, i2) => i2 & 1 && v == value))\n          result.push(this.data[i], value);\n      }\n    }\n    return result;\n  }\n  /**\n  Configure the parser. Returns a new parser instance that has the\n  given settings modified. Settings not provided in `config` are\n  kept from the original parser.\n  */\n  configure(config) {\n    let copy = Object.assign(Object.create(LRParser.prototype), this);\n    if (config.props)\n      copy.nodeSet = this.nodeSet.extend(...config.props);\n    if (config.top) {\n      let info = this.topRules[config.top];\n      if (!info)\n        throw new RangeError(`Invalid top rule name ${config.top}`);\n      copy.top = info;\n    }\n    if (config.tokenizers)\n      copy.tokenizers = this.tokenizers.map((t) => {\n        let found = config.tokenizers.find((r) => r.from == t);\n        return found ? found.to : t;\n      });\n    if (config.specializers) {\n      copy.specializers = this.specializers.slice();\n      copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n        let found = config.specializers.find((r) => r.from == s.external);\n        if (!found)\n          return s;\n        let spec = Object.assign(Object.assign({}, s), { external: found.to });\n        copy.specializers[i] = getSpecializer(spec);\n        return spec;\n      });\n    }\n    if (config.contextTracker)\n      copy.context = config.contextTracker;\n    if (config.dialect)\n      copy.dialect = this.parseDialect(config.dialect);\n    if (config.strict != null)\n      copy.strict = config.strict;\n    if (config.wrap)\n      copy.wrappers = copy.wrappers.concat(config.wrap);\n    if (config.bufferLength != null)\n      copy.bufferLength = config.bufferLength;\n    return copy;\n  }\n  /**\n  Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n  are registered for this parser.\n  */\n  hasWrappers() {\n    return this.wrappers.length > 0;\n  }\n  /**\n  Returns the name associated with a given term. This will only\n  work for all terms when the parser was generated with the\n  `--names` option. By default, only the names of tagged terms are\n  stored.\n  */\n  getName(term) {\n    return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n  }\n  /**\n  The eof term id is always allocated directly after the node\n  types. @internal\n  */\n  get eofTerm() {\n    return this.maxNode + 1;\n  }\n  /**\n  The type of top node produced by the parser.\n  */\n  get topNode() {\n    return this.nodeSet.types[this.top[1]];\n  }\n  /**\n  @internal\n  */\n  dynamicPrecedence(term) {\n    let prec = this.dynamicPrecedences;\n    return prec == null ? 0 : prec[term] || 0;\n  }\n  /**\n  @internal\n  */\n  parseDialect(dialect) {\n    let values = Object.keys(this.dialects), flags = values.map(() => false);\n    if (dialect)\n      for (let part of dialect.split(\" \")) {\n        let id2 = values.indexOf(part);\n        if (id2 >= 0)\n          flags[id2] = true;\n      }\n    let disabled = null;\n    for (let i = 0; i < values.length; i++)\n      if (!flags[i]) {\n        for (let j = this.dialects[values[i]], id2; (id2 = this.data[j++]) != 65535; )\n          (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id2] = 1;\n      }\n    return new Dialect(dialect, flags, disabled);\n  }\n  /**\n  Used by the output of the parser generator. Not available to\n  user code. @hide\n  */\n  static deserialize(spec) {\n    return new LRParser(spec);\n  }\n}\nfunction pair(data, off) {\n  return data[off] | data[off + 1] << 16;\n}\nfunction findFinished(stacks) {\n  let best = null;\n  for (let stack of stacks) {\n    let stopped = stack.p.stoppedAt;\n    if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) && stack.p.parser.stateFlag(\n      stack.state,\n      2\n      /* StateFlag.Accepting */\n    ) && (!best || best.score < stack.score))\n      best = stack;\n  }\n  return best;\n}\nfunction getSpecializer(spec) {\n  if (spec.external) {\n    let mask = spec.extend ? 1 : 0;\n    return (value, stack) => spec.external(value, stack) << 1 | mask;\n  }\n  return spec.get;\n}\nexport {\n  ContextTracker as C,\n  ExternalTokenizer as E,\n  LRParser as L,\n  LocalTokenGroup as a\n};\n"], "names": [], "mappings": ";;AACA,MAAM,KAAK,CAAC;AACZ;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,GAAG,CAAC,EAAE,MAAM,EAAE;AAC7G,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9H,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE;AAClC,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;AAC9B,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5G,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAC5D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7E,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,MAAM,EAAE;AACjB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,KAAK,GAAG,MAAM,IAAI,EAAE,EAAE,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;AACpD,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAC5B,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,KAAK;AACb,MAAM,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AAC1B,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE;AACpB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7E,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,aAAa;AACrC,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACtE,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/E,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACnG,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,EAAE;AACxH,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,qBAAqB,EAAE;AACjD,QAAQ,IAAI,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;AACnC,QAAQ,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAC3C,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG,IAAI,EAAE;AACrD,QAAQ,IAAI,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,CAAC;AAC7C,QAAQ,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;AAChH,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,aAAa,IAAI,MAAM,GAAG,MAAM,EAAE;AACxD,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS;AAChC,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,CAAC;AACT;AACA,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,MAAM,EAAE;AACzB,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK,MAAM;AACX,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAC7C,MAAM,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI;AACnC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,EAAE;AAC1D,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE;AACvH,MAAM,IAAI,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;AAClC,QAAQ,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC;AACrD,QAAQ,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AACzB,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3E,QAAQ,IAAI,KAAK,IAAI,GAAG;AACxB,UAAU,OAAO;AACjB,QAAQ,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE;AAC1C,UAAU,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACpC,UAAU,OAAO;AACjB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE;AACtC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACrC,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AAClD,QAAQ,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;AAC1D,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtD,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC1D,UAAU,KAAK,IAAI,CAAC,CAAC;AACrB,UAAU,IAAI,IAAI,GAAG,CAAC;AACtB,YAAY,IAAI,IAAI,CAAC,CAAC;AACtB,SAAS;AACT,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAChC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACrC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACnC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AACpC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;AAClC,IAAI,IAAI,MAAM,GAAG,MAAM,EAAE;AACzB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/C,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,EAAE;AACvC,MAAM,IAAI,SAAS,GAAG,MAAM,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAClD,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;AACpD,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS;AAC7B,UAAU,SAAS;AACnB,UAAU,CAAC;AACX;AACA,SAAS;AACT,UAAU,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AAC/B,OAAO;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrC,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO;AAChC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO;AACvC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE;AAC1C,IAAI,IAAI,MAAM,GAAG,KAAK;AACtB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1B;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;AACvB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACzC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE;AACpD,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,MAAM,KAAK,EAAE,CAAC;AACd,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;AACrD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI;AACpB,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,IAAI,CAAC,SAAS;AACpB,MAAM,CAAC,CAAC;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,UAAU;AACvB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5I,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AACnC,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS;AAC/D,MAAM,GAAG,IAAI,CAAC,CAAC;AACf,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC;AAC1E,IAAI,OAAO,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,UAAU;AAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC1J,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;AACjC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,MAAM;AACd,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AACxC,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,EAAE;AACjB,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM;AACjD,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS;AAC1C,QAAQ,GAAG,CAAC,KAAK;AACjB,QAAQ,CAAC;AACT;AACA,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,MAAM,IAAI,CAAC;AACrB,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;AAC/B,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACzB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,IAAI,EAAE;AACxB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG;AAChC,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE;AAChE,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;AACpB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACxD,QAAQ,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;AACrF,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;AACjC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/E,UAAU,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,SAAS;AACT,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACxE,MAAM,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AACzB,QAAQ,SAAS;AACjB,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC/B,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACxD,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,MAAM,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;AACjC,MAAM,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC;AACzB,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAC5B,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS;AACjC,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,CAAC;AACP;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;AAC7B,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;AACjD,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,EAAE,EAAE,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;AACtD,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;AACjD,MAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;AAC7E,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAChD,QAAQ,IAAI,MAAM,IAAI,IAAI;AAC1B,UAAU,OAAO,KAAK,CAAC;AACvB,QAAQ,MAAM,GAAG,MAAM,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,GAAG;AACxB,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC;AACvC,IAAI,IAAI,OAAO,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AACpC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC9B,QAAQ,OAAO;AACf,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,MAAM,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK;AAClD,QAAQ,IAAI,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC;AACtC,UAAU,CAAC;AACX,aAAa,IAAI,MAAM,GAAG,KAAK,EAAE;AACjC,UAAU,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC;AAC9C,UAAU,IAAI,MAAM,GAAG,CAAC,EAAE;AAC1B,YAAY,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;AAC/E,YAAY,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;AACnF,cAAc,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACjD,WAAW;AACX,SAAS,MAAM;AACf,UAAU,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACjD,UAAU,IAAI,KAAK,IAAI,IAAI;AAC3B,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS;AACnC,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,CAAC;AACP;AACA,KAAK,EAAE;AACP,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AAC/B,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACvD,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC;AAC9B,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAC5B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS;AACvC,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,CAAC;AACP;AACA,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS;AACpC,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,CAAC;AACP;AACA,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM;AAC5E,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC;AACjD,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE;AAC5B,IAAI,IAAI,IAAI,CAAC,UAAU;AACvB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzH,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;AAC7B,IAAI,IAAI,IAAI,CAAC,UAAU;AACvB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1H,GAAG;AACH;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACrE,GAAG;AACH;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/D,GAAG;AACH,EAAE,aAAa,CAAC,OAAO,EAAE;AACzB,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,KAAK,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACrE,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,EAAE,YAAY,CAAC,SAAS,EAAE;AAC1B,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE;AACpC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM;AACzD,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC;AAC1B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,GAAG;AACH,CAAC;AACD,MAAM,YAAY,CAAC;AACnB,EAAE,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE;AAChC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC3D,GAAG;AACH,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAClC,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,EAAE;AACjB,IAAI,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK,EAAE,KAAK,GAAG,MAAM,IAAI,EAAE,CAAC;AACpD,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE;AACpB,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;AACxC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACxC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAClF,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,GAAG;AACH,CAAC;AACD,MAAM,iBAAiB,CAAC;AACxB,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;AACvB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE;AACrE,IAAI,OAAO,IAAI,iBAAiB,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACrE,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACjC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACtB,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC3D,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAClB,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;AACvB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE,GAAG;AACH,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE,IAAI,GAAG,WAAW,EAAE;AAChD,EAAE,IAAI,OAAO,KAAK,IAAI,QAAQ;AAC9B,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB,EAAE,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI;AACnD,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,WAAW;AACf,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC;AACvD,MAAM,IAAI,IAAI,IAAI,GAAG,EAAE;AACvB,QAAQ,KAAK,GAAG,KAAK,CAAC;AACtB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,IAAI,IAAI,IAAI,EAAE;AACpB,QAAQ,IAAI,EAAE,CAAC;AACf,MAAM,IAAI,IAAI,IAAI,EAAE;AACpB,QAAQ,IAAI,EAAE,CAAC;AACf,MAAM,IAAI,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAC5B,MAAM,IAAI,KAAK,IAAI,EAAE,EAAE;AACvB,QAAQ,KAAK,IAAI,EAAE,CAAC;AACpB,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,KAAK,IAAI,KAAK,CAAC;AACrB,MAAM,IAAI,IAAI;AACd,QAAQ,MAAM;AACd,MAAM,KAAK,IAAI,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,KAAK;AACb,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;AAC3B;AACA,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,MAAM,WAAW,CAAC;AAClB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,GAAG;AACH,CAAC;AACD,MAAM,SAAS,GAAG,IAAI,WAAW,EAAE,CAAC;AACpC,MAAM,WAAW,CAAC;AAClB;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AAC3B,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,GAAG;AACH;AACA;AACA;AACA,EAAE,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE;AAC/B,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;AACpD,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;AAChC,IAAI,OAAO,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;AAC7B,MAAM,IAAI,CAAC,KAAK;AAChB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AACtC,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;AAClC,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,IAAI,KAAK,CAAC,EAAE,EAAE;AACzD,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACzC,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AACtC,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;AAClC,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG,EAAE;AACf,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;AACrD,MAAM,OAAO,GAAG,CAAC;AACjB,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM;AACjC,MAAM,IAAI,KAAK,CAAC,EAAE,GAAG,GAAG;AACxB,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC;AACpB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC;AAClD,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;AAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACnD,MAAM,IAAI,QAAQ,IAAI,IAAI;AAC1B,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,GAAG,GAAG,QAAQ,CAAC;AACrB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC9E,QAAQ,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9D,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACpD,QAAQ,OAAO,KAAK,CAAC,EAAE,IAAI,GAAG;AAC9B,UAAU,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;AAC7D,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE;AAC/C,UAAU,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AAC7D,QAAQ,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;AACnC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;AACrC,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,SAAS,GAAG,CAAC,EAAE;AACpC,IAAI,IAAI,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AACvE,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;AAC7C,MAAM,MAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACzB,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACtF,MAAM,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AACrC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B,MAAM,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAChC,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrC,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;AAC5C,MAAM,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;AAClG,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;AAC/B,MAAM,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAC5C,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;AAC5C,QAAQ,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;AACvB,IAAI,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACnD,QAAQ,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;AAC9B,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACpC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAClD,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAClB,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;AACxC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;AACxC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE;AACpB,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACzB,MAAM,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;AACxB,MAAM,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;AAChC,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE;AACzB,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE;AAC3B,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;AAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACpD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACpD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAC3E,QAAQ,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACxB,QAAQ,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE;AACjB,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;AACxE,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;AAC3E,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3E,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AACtD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAC/B,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE;AACtB,QAAQ,MAAM;AACd,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI;AACrB,QAAQ,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9E,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,CAAC;AACD,MAAM,UAAU,CAAC;AACjB,EAAE,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;AAClB,GAAG;AACH,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE;AACtB,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;AACpF,GAAG;AACH,CAAC;AACD,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;AACtG,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACnE,GAAG;AACH,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE;AACtB,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC;AACvC,IAAI,WAAW;AACf,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACvE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChC,QAAQ,MAAM;AACd,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI;AAChC,QAAQ,OAAO;AACf,MAAM,IAAI,CAAC,KAAK;AAChB,QAAQ,OAAO,EAAE,CAAC;AAClB,MAAM,IAAI,OAAO,IAAI,IAAI;AACzB,QAAQ,MAAM;AACd,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACtC,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;AAC3G,MAAM,iBAAiB,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;AACnC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;AAC3C,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;AACvC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;AACnC,GAAG;AACH,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE;AACrE,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;AACtE,EAAE,IAAI;AACN,IAAI,WAAW;AACf,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACxC,QAAQ,MAAM;AACd,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACnC,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC;AAChD,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,IAAI,CAAC,EAAE;AAC3C,UAAU,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;AAC3J,YAAY,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpC,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC7D,MAAM,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE;AAChF,QAAQ,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,QAAQ,SAAS,IAAI,CAAC;AACtB,OAAO;AACP,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI;AAC3B,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AAC9C,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;AAC9D,QAAQ,IAAI,IAAI,GAAG,IAAI;AACvB,UAAU,IAAI,GAAG,GAAG,CAAC;AACrB,aAAa,IAAI,IAAI,IAAI,EAAE;AAC3B,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACxB,aAAa;AACb,UAAU,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAClC,UAAU,KAAK,CAAC,OAAO,EAAE,CAAC;AAC1B,UAAU,SAAS,IAAI,CAAC;AACxB,SAAS;AACT,OAAO;AACP,MAAM,MAAM;AACZ,KAAK;AACL,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;AACvC,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,EAAE;AAC1D,IAAI,IAAI,IAAI,IAAI,IAAI;AACpB,MAAM,OAAO,CAAC,GAAG,KAAK,CAAC;AACvB,EAAE,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE;AACxD,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,EAAE,OAAO,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AACxE,CAAC;AACD,MAAM,OAAO,GAAG,OAAO,OAAO,IAAI,WAAW,IAAI,OAAO,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClG,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,SAAS,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;AAChC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACtD,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACrB,EAAE,WAAW;AACb,IAAI,IAAI,EAAE,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtE,MAAM,WAAW;AACjB,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;AACpF,UAAU,OAAO,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG;AAChD,YAAY,MAAM,CAAC,EAAE,GAAG,CAAC;AACzB,YAAY,GAAG,GAAG,EAAE;AACpB;AACA,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AAC7C,YAAY,MAAM,CAAC,IAAI,GAAG,CAAC;AAC3B,YAAY,GAAG,GAAG,EAAE;AACpB;AACA,WAAW,CAAC,CAAC;AACb,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE;AAClE,UAAU,MAAM;AAChB,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC5B,UAAU,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5C,OAAO;AACP,GAAG;AACH,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/F,IAAI,IAAI,EAAE,EAAE;AACZ,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;AAClG,MAAM,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;AAC3F,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAChC,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,OAAO;AACP,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AAC3B,KAAK;AACL,GAAG;AACH;AACA,EAAE,MAAM,CAAC,GAAG,EAAE;AACd,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS;AAC5B,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG;AAC9C,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ;AACtB,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,WAAW;AACf,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;AACpB,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3D,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE;AACxC,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACzB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC1D,MAAM,IAAI,KAAK,GAAG,GAAG,EAAE;AACvB,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,IAAI,IAAI,YAAY,IAAI,EAAE;AAChC,QAAQ,IAAI,KAAK,IAAI,GAAG,EAAE;AAC1B,UAAU,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ;AACnC,YAAY,OAAO,IAAI,CAAC;AACxB,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AACxC,UAAU,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,YAAY,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC1D,YAAY,IAAI,CAAC,SAAS,IAAI,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;AAChE,cAAc,OAAO,IAAI,CAAC;AAC1B,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3B,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACjE,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3B,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7C,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,UAAU,CAAC;AACjB,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;AACtD,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS;AAC/B,MAAM,KAAK,CAAC,KAAK;AACjB,MAAM,CAAC;AACP;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AAC/D,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC;AAC9B,QAAQ,SAAS;AACjB,MAAM,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5D,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ;AACrC,QAAQ,SAAS;AACjB,MAAM,IAAI,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,IAAI,OAAO,EAAE;AAC9G,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACxD,QAAQ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAC1B,QAAQ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE;AAC1C,QAAQ,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACzD,MAAM,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE;AAC5B,QAAQ,IAAI,UAAU,GAAG,WAAW,CAAC;AACrC,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/B,UAAU,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACvF,QAAQ,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAClF,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC/B,UAAU,IAAI,GAAG,KAAK,CAAC;AACvB,UAAU,IAAI,WAAW,GAAG,UAAU;AACtC,YAAY,MAAM;AAClB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,WAAW;AAC5C,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,SAAS;AACjB,MAAM,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;AAC/C,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAC/B,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;AAC1C,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACxC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC9E,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,SAAS;AACtB,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;AAC5B,IAAI,IAAI,IAAI,GAAG,IAAI,WAAW,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AACrD,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE;AAC7C,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AAC5D,IAAI,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;AAC1B,MAAM,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAC/B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE;AACxD,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE;AAClD,UAAU,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/F,UAAU,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE;AACzE,YAAY,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;AACjC,cAAc,KAAK,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,CAAC;AACxC;AACA,cAAc,KAAK,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,CAAC;AAC3C,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACtB,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACvC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC;AACrC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM;AACnC,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;AAChC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACvC,IAAI,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AACnE,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AACtC,MAAM,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS;AACnC,QAAQ,KAAK;AACb,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC;AACnB;AACA,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;AACnB,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;AAC9B,UAAU,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE;AAChC,YAAY,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,WAAW,MAAM;AACjB,YAAY,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC9C,cAAc,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC3E,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;AAC5B,UAAU,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACvE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,CAAC;AACD,MAAM,KAAK,CAAC;AACZ,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;AAChD,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,GAAG,IAAI,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AACjJ,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;AACrD,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrC,IAAI,IAAI,OAAO,EAAE,aAAa,CAAC;AAC/B,IAAI,IAAI,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;AAC5D,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AACvB,MAAM,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC7G,OAAO;AACP,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,WAAW;AACjB,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AACrC,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;AAC7B,UAAU,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,SAAS,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;AAChE,UAAU,SAAS;AACnB,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,OAAO,EAAE;AACxB,YAAY,OAAO,GAAG,EAAE,CAAC;AACzB,YAAY,aAAa,GAAG,EAAE,CAAC;AAC/B,WAAW;AACX,UAAU,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,UAAU,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACpD,UAAU,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACjD,SAAS;AACT,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3B,MAAM,IAAI,QAAQ,GAAG,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;AACtD,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/D,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC1C,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC9B,QAAQ,IAAI,OAAO,IAAI,OAAO;AAC9B,UAAU,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACjI,QAAQ,MAAM,IAAI,WAAW,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC;AACpD,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU;AAC1B,QAAQ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,EAAE;AACpC,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;AAClJ,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChE,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;AACrD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;AACzB,MAAM,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxE,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,YAAY,EAAE;AAC3C,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACpD,QAAQ,OAAO,SAAS,CAAC,MAAM,GAAG,YAAY;AAC9C,UAAU,SAAS,CAAC,GAAG,EAAE,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC;AAClD,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,KAAK,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,MAAM,KAAK;AACX,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACvD,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACnC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,YAAY,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,YAAY,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;AAClG,cAAc,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;AAChG,gBAAgB,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACzC,eAAe,MAAM;AACrB,gBAAgB,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACzC,gBAAgB,SAAS,KAAK,CAAC;AAC/B,eAAe;AACf,aAAa;AACb,WAAW;AACX,SAAS;AACT,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE;AAC/B,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,EAAE;AACZ,UAAU,SAAS,CAAC,MAAM,GAAG,EAAE;AAC/B;AACA,SAAS,CAAC;AACV,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACxC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;AAC7C,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW;AAC7C,QAAQ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC5C,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,EAAE;AACd,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;AACtD,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC7C,IAAI,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC;AAC3D,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS;AACxD,MAAM,OAAO,KAAK,CAAC,WAAW,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM,IAAI,QAAQ,GAAG,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;AACxH,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,IAAI;AAChE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChI,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE;AAC9G,UAAU,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACvC,UAAU,IAAI,OAAO;AACrB,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1G,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,QAAQ,IAAI,EAAE,MAAM,YAAY,IAAI,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/F,UAAU,MAAM;AAChB,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;AAC7D,UAAU,MAAM,GAAG,KAAK,CAAC;AACzB;AACA,UAAU,MAAM;AAChB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,MAAM,CAAC,SAAS;AACxC,MAAM,KAAK,CAAC,KAAK;AACjB,MAAM,CAAC;AACP;AACA,KAAK,CAAC;AACN,IAAI,IAAI,aAAa,GAAG,CAAC,EAAE;AAC3B,MAAM,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAClC,MAAM,IAAI,OAAO;AACjB,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,OAAO;AACtF,UAAU,aAAa,GAAG,KAAK;AAC/B;AACA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACd,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;AACpC,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;AAC9D,OAAO;AACP,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI;AAC1C,MAAM,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACzE,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;AAC/C,MAAM,IAAI,UAAU,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACpD,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AACvC,MAAM,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9E,MAAM,IAAI,OAAO;AACjB,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO;AAC5H,UAAU,MAAM,GAAG,KAAK;AACxB;AACA,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,UAAU,IAAI,KAAK,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F,MAAM,IAAI,IAAI;AACd,QAAQ,OAAO,IAAI,CAAC;AACpB,WAAW,IAAI,UAAU,CAAC,GAAG,GAAG,KAAK;AACrC,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC;AACA,QAAQ,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE;AACjC,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,WAAW;AACf,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;AAC/C,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;AAC3B,QAAQ,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACzC,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE;AACzC,IAAI,IAAI,QAAQ,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF,MAAM,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC;AAC7D,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,SAAS;AACrB,UAAU,SAAS;AACnB,QAAQ,SAAS,GAAG,IAAI,CAAC;AACzB,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC;AACxB,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,CAAC;AACnE,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACvD,QAAQ,IAAI,IAAI;AAChB,UAAU,SAAS;AACnB,OAAO;AACP,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG,IAAI,CAAC;AAClD,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAC1D,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,qBAAqB,CAAC,CAAC;AAC/E,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACvD,QAAQ,IAAI,IAAI;AAChB,UAAU,MAAM;AAChB,QAAQ,IAAI,OAAO;AACnB,UAAU,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AACnD,OAAO;AACP,MAAM,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;AACvD,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC,CAAC;AAC7E,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC7C,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE;AACvC,QAAQ,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,EAAE;AACnC,UAAU,QAAQ,EAAE,CAAC;AACrB,UAAU,KAAK,GAAG,CAAC,CAAC;AACpB,SAAS;AACT,QAAQ,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/C,QAAQ,IAAI,OAAO;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1G,QAAQ,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACzC,OAAO,MAAM,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC5D,QAAQ,QAAQ,GAAG,KAAK,CAAC;AACzB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;AACA,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,MAAM,MAAM,EAAE,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC;AAC7C,MAAM,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;AAClC,MAAM,KAAK,EAAE,IAAI,CAAC,OAAO;AACzB,MAAM,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;AAC/C,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;AAChC,MAAM,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;AAC7C,MAAM,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;AAC9C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,KAAK,EAAE;AACjB,IAAI,IAAI,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,mBAAmB,IAAI,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAClF,IAAI,IAAI,CAAC,GAAG;AACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,GAAG,GAAG,KAAK,CAAC;AACvB,GAAG;AACH,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE;AAC1C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,IAAI,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC1D,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;AAC1C,QAAQ,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,CAAC;AACD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;AACvC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD,GAAG;AACH,CAAC;AACD,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACpB,MAAM,cAAc,CAAC;AACrB;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAClC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;AACxC,GAAG;AACH,CAAC;AACD,MAAM,QAAQ,SAAS,MAAM,CAAC;AAC9B;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,EAAE;AAC1B,MAAM,MAAM,IAAI,UAAU,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrG,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC;AAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACjD,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;AAC7C,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,IAAI,SAAS,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE;AAC1C,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS;AACtB,MAAM,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;AAC3C,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAQ,IAAI,OAAO,IAAI,IAAI,QAAQ;AACnC,UAAU,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,IAAI;AAC/C,UAAU,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,UAAU,IAAI,IAAI,IAAI,CAAC,EAAE;AACzB,YAAY,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,WAAW,MAAM;AACjB,YAAY,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC1C,cAAc,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAClD,YAAY,CAAC,EAAE,CAAC;AAChB,WAAW;AACX,SAAS;AACT,OAAO;AACP,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,MAAM,CAAC;AAC1E,MAAM,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,GAAG,IAAI;AACnD,MAAM,EAAE,EAAE,CAAC;AACX,MAAM,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,MAAM,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnC,MAAM,KAAK,EAAE,CAAC,IAAI,CAAC;AACnB,MAAM,OAAO,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC,CAAC,CAAC;AACT,IAAI,IAAI,IAAI,CAAC,WAAW;AACxB,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;AAC5C,IAAI,IAAI,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAChC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;AACnD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACrE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE;AACzD,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC1D,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAChC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,OAAO,KAAK,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3H,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;AACxC,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC;AAC9D,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;AAC5C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACvC,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC1D,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ;AAC/B,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE;AACtC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;AACxB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM;AACxC,MAAM,IAAI,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;AACvD,MAAM,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,IAAI,KAAK;AACvB,QAAQ,OAAO,MAAM,CAAC;AACtB,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI,QAAQ,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE;AAC5D,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK;AAC/B,UAAU,OAAO,MAAM,CAAC;AACxB,MAAM,IAAI,IAAI;AACd,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE;AAC7B,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AACtC,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS;AACjC,QAAQ,KAAK;AACb,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC;AACnB;AACA,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;AACzB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AACvC,UAAU,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC9B,YAAY,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,eAAe,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACnC,YAAY,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC;AACA,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC;AACzC,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE;AACzB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS;AAC1B,MAAM,KAAK;AACX,MAAM,CAAC;AACP;AACA,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE;AAC7B,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;AACtE,GAAG;AACH;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE;AAC5B,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS;AAC9B,MAAM,KAAK;AACX,MAAM,CAAC;AACP;AACA,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAChD,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS;AAC/B,MAAM,KAAK;AACX,MAAM,CAAC;AACP;AACA,KAAK,EAAE,MAAM,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/B,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;AACjC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACjC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC;AACA,UAAU,MAAM;AAChB,OAAO;AACP,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS;AAC/B,MAAM,KAAK;AACX,MAAM,CAAC;AACP;AACA,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;AACjB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;AACjC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACjC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC;AACA,UAAU,MAAM;AAChB,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE;AACjD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;AACzD,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;AACtE,IAAI,IAAI,MAAM,CAAC,KAAK;AACpB,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE;AACpB,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,IAAI;AACf,QAAQ,MAAM,IAAI,UAAU,CAAC,CAAC,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACpE,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,UAAU;AACzB,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AACnD,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;AAC/D,QAAQ,OAAO,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AACpC,OAAO,CAAC,CAAC;AACT,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE;AAC7B,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;AACpD,MAAM,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAClE,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC1E,QAAQ,IAAI,CAAC,KAAK;AAClB,UAAU,OAAO,CAAC,CAAC;AACnB,QAAQ,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/E,QAAQ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AACpD,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,cAAc;AAC7B,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC;AAC3C,IAAI,IAAI,MAAM,CAAC,OAAO;AACtB,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACvD,IAAI,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI;AAC7B,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAClC,IAAI,IAAI,MAAM,CAAC,IAAI;AACnB,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxD,IAAI,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI;AACnC,MAAM,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAC9C,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AACzH,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACvC,IAAI,OAAO,IAAI,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA,EAAE,YAAY,CAAC,OAAO,EAAE;AACxB,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;AAC7E,IAAI,IAAI,OAAO;AACf,MAAM,KAAK,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC3C,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC5B,OAAO;AACP,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC;AACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE;AAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACrB,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK;AACnF,UAAU,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/E,OAAO;AACP,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;AAC3B,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9B,GAAG;AACH,CAAC;AACD,SAAS,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACzC,CAAC;AACD,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC;AAClB,EAAE,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;AAC5B,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;AACpC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS;AAC/G,MAAM,KAAK,CAAC,KAAK;AACjB,MAAM,CAAC;AACP;AACA,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC5C,MAAM,IAAI,GAAG,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE;AAC9B,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACrB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACrE,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AAClB;;;;"}