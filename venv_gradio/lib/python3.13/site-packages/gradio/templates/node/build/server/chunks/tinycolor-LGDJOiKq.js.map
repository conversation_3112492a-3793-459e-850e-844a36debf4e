{"version": 3, "file": "tinycolor-LGDJOiKq.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/tinycolor.js"], "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj2) {\n    return typeof obj2;\n  } : function(obj2) {\n    return obj2 && \"function\" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? \"symbol\" : typeof obj2;\n  }, _typeof(obj);\n}\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n  if (this._r < 1)\n    this._r = Math.round(this._r);\n  if (this._g < 1)\n    this._g = Math.round(this._g);\n  if (this._b < 1)\n    this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1e3;\n  },\n  getLuminance: function getLuminance() {\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928)\n      R = RsRGB / 12.92;\n    else\n      R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928)\n      G = GsRGB / 12.92;\n    else\n      G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928)\n      B = BsRGB / 12.92;\n    else\n      B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360), s = Math.round(hsv.s * 100), v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360), s = Math.round(hsl.s * 100), l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\ntinycolor.fromRatio = function(color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a\n  };\n}\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b), min = Math.min(r, g, b);\n  var h, s, l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0;\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h,\n    s,\n    l\n  };\n}\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p2, q2, t) {\n    if (t < 0)\n      t += 1;\n    if (t > 1)\n      t -= 1;\n    if (t < 1 / 6)\n      return p2 + (q2 - p2) * 6 * t;\n    if (t < 1 / 2)\n      return q2;\n    if (t < 2 / 3)\n      return p2 + (q2 - p2) * (2 / 3 - t) * 6;\n    return p2;\n  }\n  if (s === 0) {\n    r = g = b = l;\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b), min = Math.min(r, g, b);\n  var h, s, v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0;\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h,\n    s,\n    v\n  };\n}\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h), f = h - i, p = v * (1 - s), q = v * (1 - f * s), t = v * (1 - (1 - f) * s), mod = i % 6, r = [v, q, p, p, t, v][mod], g = [t, v, v, q, p, p][mod], b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\ntinycolor.equals = function(color1, color2) {\n  if (!color1 || !color2)\n    return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function() {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results; ) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h, s = hsv.s, v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h,\n      s,\n      v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\ntinycolor.mix = function(color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\ntinycolor.readability = function(color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\ntinycolor.isReadable = function(color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\ntinycolor.mostReadable = function(baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level,\n    size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\nvar hexNames = tinycolor.hexNames = flip(names);\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\nfunction bound01(n, max) {\n  if (isOnePointZero(n))\n    n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n  if (Math.abs(n - max) < 1e-6) {\n    return 1;\n  }\n  return n % max / parseFloat(max);\n}\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function() {\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level,\n    size\n  };\n}\nexport {\n  tinycolor as t\n};\n"], "names": [], "mappings": "AAAA,SAAS,OAAO,CAAC,GAAG,EAAE;AACtB,EAAE,yBAAyB,CAAC;AAC5B,EAAE,OAAO,OAAO,GAAG,UAAU,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE;AACtG,IAAI,OAAO,OAAO,IAAI,CAAC;AACvB,GAAG,GAAG,SAAS,IAAI,EAAE;AACrB,IAAI,OAAO,IAAI,IAAI,UAAU,IAAI,OAAO,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,OAAO,IAAI,CAAC;AACpI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAClB,CAAC;AACD,IAAI,QAAQ,GAAG,MAAM,CAAC;AACtB,IAAI,SAAS,GAAG,MAAM,CAAC;AACvB,SAAS,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE;AAChC,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC7B,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACpB,EAAE,IAAI,KAAK,YAAY,SAAS,EAAE;AAClC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,EAAE,IAAI,YAAY,SAAS,CAAC,EAAE;AACpC,IAAI,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC;AAC5L,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;AACjB,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;AACjB,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC;AACjB,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;AACpB,CAAC;AACD,SAAS,CAAC,SAAS,GAAG;AACtB,EAAE,MAAM,EAAE,SAAS,MAAM,GAAG;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE,GAAG,GAAG,CAAC;AACtC,GAAG;AACH,EAAE,OAAO,EAAE,SAAS,OAAO,GAAG;AAC9B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,GAAG;AACH,EAAE,OAAO,EAAE,SAAS,OAAO,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC;AACpB,GAAG;AACH,EAAE,gBAAgB,EAAE,SAAS,gBAAgB,GAAG;AAChD,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,GAAG;AACH,EAAE,SAAS,EAAE,SAAS,SAAS,GAAG;AAClC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,GAAG;AAChC,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;AACnB,GAAG;AACH,EAAE,aAAa,EAAE,SAAS,aAAa,GAAG;AAC1C,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;AAC3D,GAAG;AACH,EAAE,YAAY,EAAE,SAAS,YAAY,GAAG;AACxC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3B,IAAI,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACxB,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACxB,IAAI,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACxB,IAAI,IAAI,KAAK,IAAI,OAAO;AACxB,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AACxB;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;AACjD,IAAI,IAAI,KAAK,IAAI,OAAO;AACxB,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AACxB;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;AACjD,IAAI,IAAI,KAAK,IAAI,OAAO;AACxB,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AACxB;AACA,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC;AACjD,IAAI,OAAO,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;AACrC,IAAI,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG;AAC1B,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;AACpB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE;AAChB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE,SAAS,WAAW,GAAG;AACtC,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9F,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AACrI,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG;AAC1B,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;AACpB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE;AAChB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE,SAAS,WAAW,GAAG;AACtC,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC9F,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AACrI,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,KAAK,CAAC,UAAU,EAAE;AACpC,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,WAAW,EAAE,SAAS,WAAW,CAAC,UAAU,EAAE;AAChD,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,MAAM,CAAC,UAAU,EAAE;AACtC,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;AACrE,GAAG;AACH,EAAE,YAAY,EAAE,SAAS,YAAY,CAAC,UAAU,EAAE;AAClD,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG;AAC1B,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5B,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5B,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5B,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE;AAChB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE,SAAS,WAAW,GAAG;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AAC7O,GAAG;AACH,EAAE,eAAe,EAAE,SAAS,eAAe,GAAG;AAC9C,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;AACtD,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;AACtD,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;AACtD,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE;AAChB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,qBAAqB,EAAE,SAAS,qBAAqB,GAAG;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;AAC3W,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,MAAM,GAAG;AAC5B,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;AACvB,MAAM,OAAO,aAAa,CAAC;AAC3B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;AACrB,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;AACxE,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,CAAC,WAAW,EAAE;AAC3C,IAAI,IAAI,UAAU,GAAG,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7E,IAAI,IAAI,gBAAgB,GAAG,UAAU,CAAC;AACtC,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,GAAG,oBAAoB,GAAG,EAAE,CAAC;AACtE,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;AACrC,MAAM,gBAAgB,GAAG,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,OAAO,6CAA6C,GAAG,YAAY,GAAG,gBAAgB,GAAG,UAAU,GAAG,eAAe,GAAG,gBAAgB,GAAG,GAAG,CAAC;AACnJ,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;AACtC,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC;AAC7B,IAAI,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;AACpC,IAAI,IAAI,eAAe,GAAG,KAAK,CAAC;AAChC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,gBAAgB,GAAG,CAAC,SAAS,IAAI,QAAQ,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC;AACjL,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;AAC9C,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;AAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,EAAE;AAC/C,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;AAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;AAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,eAAe,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACjD,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG;AAC1B,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,kBAAkB,EAAE,SAAS,kBAAkB,CAAC,EAAE,EAAE,IAAI,EAAE;AAC5D,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,EAAE,SAAS,OAAO,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,GAAG;AAChC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,MAAM,GAAG;AAC5B,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,UAAU,EAAE,SAAS,UAAU,GAAG;AACpC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,QAAQ,EAAE,SAAS,QAAQ,GAAG;AAChC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,SAAS,EAAE,SAAS,SAAS,GAAG;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,IAAI,EAAE,SAAS,IAAI,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,iBAAiB,EAAE,SAAS,iBAAiB,CAAC,EAAE,EAAE,IAAI,EAAE;AAC1D,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,SAAS,EAAE,SAAS,SAAS,GAAG;AAClC,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,UAAU,EAAE,SAAS,UAAU,GAAG;AACpC,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,aAAa,EAAE,SAAS,aAAa,GAAG;AAC1C,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,eAAe,EAAE,SAAS,eAAe,GAAG;AAC9C,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAC/D,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG;AAC1B,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,MAAM,GAAG;AAC5B,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,GAAG;AACH,CAAC,CAAC;AACF,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK,EAAE,IAAI,EAAE;AAC5C,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC;AACtB,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE;AACzB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;AACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE;AACvB,UAAU,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,SAAS,MAAM;AACf,UAAU,QAAQ,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,KAAK,GAAG,QAAQ,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC;AACF,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AACf,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AACf,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AACf,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACjB,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AAChC,IAAI,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACvF,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,MAAM,EAAE,GAAG,IAAI,CAAC;AAChB,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;AACnE,KAAK,MAAM,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC9F,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,MAAM,EAAE,GAAG,IAAI,CAAC;AAChB,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,KAAK,MAAM,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC9F,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,MAAM,EAAE,GAAG,IAAI,CAAC;AAChB,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACnC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,MAAM;AAClC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,CAAC;AACL,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;AAC5B,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;AAC5B,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG;AAC5B,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AAChC,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;AAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACd,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACxD,IAAI,QAAQ,GAAG;AACf,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C,QAAQ,MAAM;AACd,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,KAAK;AACL,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG;AACH,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,CAAC;AACL,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACd,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,GAAG,CAAC;AACb,MAAM,CAAC,IAAI,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,GAAG,CAAC;AACb,MAAM,CAAC,IAAI,CAAC,CAAC;AACb,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACjB,MAAM,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACjB,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACjB,MAAM,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClB,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtB,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACpB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACpB,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC9B,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;AAClB,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG;AACf,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C,QAAQ,MAAM;AACd,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,MAAM,KAAK,CAAC;AACZ,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,KAAK;AACL,IAAI,CAAC,IAAI,CAAC,CAAC;AACX,GAAG;AACH,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,CAAC;AACL,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACxM,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACd,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;AACvC,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnH,EAAE,IAAI,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAC1I,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC;AACD,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;AAC3C,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjJ,EAAE,IAAI,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AAClL,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC;AACD,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACnC,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjJ,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC;AACD,SAAS,CAAC,MAAM,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE;AAC5C,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM;AACxB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;AAC5E,CAAC,CAAC;AACF,SAAS,CAAC,MAAM,GAAG,WAAW;AAC9B,EAAE,OAAO,SAAS,CAAC,SAAS,CAAC;AAC7B,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;AACpB,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;AACpB,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;AACpB,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE;AACpC,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;AACxB,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE;AAClC,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;AACxB,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC1C,CAAC;AACD,SAAS,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE;AACjC,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;AACxB,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE;AAClC,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE;AAChC,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;AACxB,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE;AAC9B,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AACnC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;AAC9B,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;AAC/B,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,IAAI,CAAC,EAAE;AACpC,IAAI,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,IAAI,MAAM,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,EAAE,IAAI,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC;AAC1B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1B,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,GAAG;AACjC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;AACd,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC;AACtC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;AACrB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,GAAG,CAAC,EAAE,SAAS,CAAC;AAChB,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;AACtB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,GAAG,CAAC,CAAC,CAAC;AACN,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE;AAC5C,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;AACzB,EAAE,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;AACxB,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,IAAI,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC;AAC1B,EAAE,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/B,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAE,OAAO,IAAI;AACzE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC;AACjC,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE;AACxC,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;AACzB,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,IAAI,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC;AACjC,EAAE,OAAO,OAAO,EAAE,EAAE;AACpB,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;AACvB,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,CAAC;AACP,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,SAAS,CAAC,GAAG,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACjD,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC;AAC3C,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;AACvC,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;AACvC,EAAE,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC,CAAC;AACF,SAAS,CAAC,WAAW,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE;AACjD,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3H,CAAC,CAAC;AACF,SAAS,CAAC,UAAU,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvD,EAAE,IAAI,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1D,EAAE,IAAI,UAAU,EAAE,GAAG,CAAC;AACtB,EAAE,GAAG,GAAG,KAAK,CAAC;AACd,EAAE,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACzC,EAAE,QAAQ,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI;AAC5C,IAAI,KAAK,SAAS,CAAC;AACnB,IAAI,KAAK,UAAU;AACnB,MAAM,GAAG,GAAG,WAAW,IAAI,GAAG,CAAC;AAC/B,MAAM,MAAM;AACZ,IAAI,KAAK,SAAS;AAClB,MAAM,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;AAC7B,MAAM,MAAM;AACZ,IAAI,KAAK,UAAU;AACnB,MAAM,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;AAC7B,MAAM,MAAM;AACZ,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,SAAS,CAAC,YAAY,GAAG,SAAS,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;AAC9D,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC;AACpB,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,qBAAqB,EAAE,KAAK,EAAE,IAAI,CAAC;AACzC,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACpB,EAAE,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACrD,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACrB,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,IAAI,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,IAAI,IAAI,WAAW,GAAG,SAAS,EAAE;AACjC,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH,EAAE,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE;AACjD,IAAI,KAAK;AACT,IAAI,IAAI;AACR,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM;AACT,IAAI,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACvC,IAAI,OAAO,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;AACrE,GAAG;AACH,CAAC,CAAC;AACF,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG;AAC9B,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,cAAc,EAAE,QAAQ;AAC1B,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,cAAc,EAAE,QAAQ;AAC1B,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,cAAc,EAAE,QAAQ;AAC1B,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,oBAAoB,EAAE,QAAQ;AAChC,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,cAAc,EAAE,KAAK;AACvB,EAAE,cAAc,EAAE,KAAK;AACvB,EAAE,cAAc,EAAE,QAAQ;AAC1B,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,gBAAgB,EAAE,QAAQ;AAC5B,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,cAAc,EAAE,QAAQ;AAC1B,EAAE,eAAe,EAAE,QAAQ;AAC3B,EAAE,iBAAiB,EAAE,QAAQ;AAC7B,EAAE,eAAe,EAAE,QAAQ;AAC3B,EAAE,eAAe,EAAE,QAAQ;AAC3B,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,OAAO,EAAE,QAAQ;AACnB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,WAAW,EAAE,QAAQ;AACvB,CAAC,CAAC;AACF,IAAI,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAChD,SAAS,IAAI,CAAC,CAAC,EAAE;AACjB,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AACnB,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;AAC7B,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,SAAS,UAAU,CAAC,CAAC,EAAE;AACvB,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAClC,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE;AACzB,EAAE,IAAI,cAAc,CAAC,CAAC,CAAC;AACvB,IAAI,CAAC,GAAG,MAAM,CAAC;AACf,EAAE,IAAI,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AACpC,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;AAChC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH,EAAE,OAAO,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,OAAO,CAAC,GAAG,EAAE;AACtB,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACvC,CAAC;AACD,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC3B,CAAC;AACD,SAAS,cAAc,CAAC,CAAC,EAAE;AAC3B,EAAE,OAAO,OAAO,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC;AACD,SAAS,YAAY,CAAC,CAAC,EAAE;AACzB,EAAE,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACvD,CAAC;AACD,SAAS,IAAI,CAAC,CAAC,EAAE;AACjB,EAAE,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1C,CAAC;AACD,SAAS,mBAAmB,CAAC,CAAC,EAAE;AAChC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACd,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,GAAG;AACH,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD,SAAS,mBAAmB,CAAC,CAAC,EAAE;AAChC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AACtD,CAAC;AACD,SAAS,mBAAmB,CAAC,CAAC,EAAE;AAChC,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAClC,CAAC;AACD,IAAI,QAAQ,GAAG,WAAW;AAC1B,EAAE,IAAI,WAAW,GAAG,eAAe,CAAC;AACpC,EAAE,IAAI,UAAU,GAAG,sBAAsB,CAAC;AAC1C,EAAE,IAAI,QAAQ,GAAG,KAAK,GAAG,UAAU,GAAG,OAAO,GAAG,WAAW,GAAG,GAAG,CAAC;AAClE,EAAE,IAAI,iBAAiB,GAAG,aAAa,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,WAAW,CAAC;AACrH,EAAE,IAAI,iBAAiB,GAAG,aAAa,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ,GAAG,WAAW,CAAC;AAC/I,EAAE,OAAO;AACT,IAAI,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC;AAClC,IAAI,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC;AAC9C,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAiB,CAAC;AAChD,IAAI,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC;AAC9C,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAiB,CAAC;AAChD,IAAI,GAAG,EAAE,IAAI,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC;AAC9C,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAiB,CAAC;AAChD,IAAI,IAAI,EAAE,sDAAsD;AAChE,IAAI,IAAI,EAAE,sDAAsD;AAChE,IAAI,IAAI,EAAE,sEAAsE;AAChF,IAAI,IAAI,EAAE,sEAAsE;AAChF,GAAG,CAAC;AACJ,CAAC,EAAE,CAAC;AACJ,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AACD,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;AAC3E,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AACpB,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACzB,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,GAAG,MAAM,IAAI,KAAK,IAAI,aAAa,EAAE;AACrC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,MAAM,EAAE,MAAM;AACpB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM;AACrC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,KAAK;AACpC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM;AACrC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO;AACX,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,KAAK;AACpC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACnC,EAAE,IAAI,KAAK,EAAE,IAAI,CAAC;AAClB,EAAE,KAAK,GAAG,KAAK,IAAI;AACnB,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,GAAG,CAAC;AACJ,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE,WAAW,EAAE,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,EAAE,WAAW,EAAE,CAAC;AAC/C,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;AACzC,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,EAAE;AAC5C,IAAI,IAAI,GAAG,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,IAAI;AACR,GAAG,CAAC;AACJ;;;;"}