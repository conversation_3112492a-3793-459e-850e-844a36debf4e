{"version": 3, "file": "index53-CCqMaMMC.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index53.js"], "sourcesContent": ["import { create_ssr_component, validate_component, escape } from \"svelte/internal\";\nimport { createEventDispatcher } from \"svelte\";\nimport { U as Upload } from \"./ModifyUpload.js\";\nimport { B as BlockLabel, V as Video$1, R as SelectSource, n as Block, S as Static, U as UploadText } from \"./client.js\";\nimport { Webcam } from \"./Index26.js\";\nimport { P as Player, p as prettyBytes, V as VideoPreview } from \"./VideoPreview.js\";\nimport { l, a } from \"./VideoPreview.js\";\nimport { default as default2 } from \"./Example25.js\";\nconst css = {\n  code: \".file-name.svelte-14jis2k{padding:var(--size-6);font-size:var(--text-xxl);word-break:break-all}.file-size.svelte-14jis2k{padding:var(--size-2);font-size:var(--text-xl)}.upload-container.svelte-14jis2k{height:100%;width:100%}.video-container.svelte-14jis2k{display:flex;height:100%;flex-direction:column;justify-content:center;align-items:center}\",\n  map: '{\"version\":3,\"file\":\"InteractiveVideo.svelte\",\"sources\":[\"InteractiveVideo.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { Upload, ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Webcam } from \\\\\"@gradio/image\\\\\";\\\\nimport { Video } from \\\\\"@gradio/icons\\\\\";\\\\nimport { prettyBytes, playable } from \\\\\"./utils\\\\\";\\\\nimport Player from \\\\\"./Player.svelte\\\\\";\\\\nimport { SelectSource } from \\\\\"@gradio/atoms\\\\\";\\\\nexport let value = null;\\\\nexport let subtitle = null;\\\\nexport let sources = [\\\\\"webcam\\\\\", \\\\\"upload\\\\\"];\\\\nexport let label = void 0;\\\\nexport let show_download_button = false;\\\\nexport let show_label = true;\\\\nexport let webcam_options;\\\\nexport let include_audio;\\\\nexport let autoplay;\\\\nexport let root;\\\\nexport let i18n;\\\\nexport let active_source = \\\\\"webcam\\\\\";\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let loop;\\\\nexport let uploading = false;\\\\nlet has_change_history = false;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction handle_load({ detail }) {\\\\n    value = detail;\\\\n    dispatch(\\\\\"change\\\\\", detail);\\\\n    dispatch(\\\\\"upload\\\\\", detail);\\\\n}\\\\nfunction handle_clear() {\\\\n    value = null;\\\\n    dispatch(\\\\\"change\\\\\", null);\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n}\\\\nfunction handle_change(video) {\\\\n    has_change_history = true;\\\\n    dispatch(\\\\\"change\\\\\", video);\\\\n}\\\\nfunction handle_capture({ detail }) {\\\\n    dispatch(\\\\\"change\\\\\", detail);\\\\n}\\\\nlet dragging = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\n<\\/script>\\\\n\\\\n<BlockLabel {show_label} Icon={Video} label={label || \\\\\"Video\\\\\"} />\\\\n<div data-testid=\\\\\"video\\\\\" class=\\\\\"video-container\\\\\">\\\\n\\\\t{#if value === null || value.url === undefined}\\\\n\\\\t\\\\t<div class=\\\\\"upload-container\\\\\">\\\\n\\\\t\\\\t\\\\t{#if active_source === \\\\\"upload\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tfiletype=\\\\\"video/x-m4v,video/*\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:load={handle_load}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:error={({ detail }) => dispatch(\\\\\"error\\\\\", detail)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria_label={i18n(\\\\\"video.drop_to_upload\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t\\\\t\\\\t</Upload>\\\\n\\\\t\\\\t\\\\t{:else if active_source === \\\\\"webcam\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<Webcam\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmirror_webcam={webcam_options.mirror}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twebcam_constraints={webcam_options.constraints}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{include_audio}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmode=\\\\\"video\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:capture={handle_capture}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:start_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:stop_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstream_every={1}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else if value?.url}\\\\n\\\\t\\\\t{#key value?.url}\\\\n\\\\t\\\\t\\\\t<Player\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\tinteractive\\\\n\\\\t\\\\t\\\\t\\\\t{autoplay}\\\\n\\\\t\\\\t\\\\t\\\\tsrc={value.url}\\\\n\\\\t\\\\t\\\\t\\\\tsubtitle={subtitle?.url}\\\\n\\\\t\\\\t\\\\t\\\\tis_stream={false}\\\\n\\\\t\\\\t\\\\t\\\\ton:play\\\\n\\\\t\\\\t\\\\t\\\\ton:pause\\\\n\\\\t\\\\t\\\\t\\\\ton:stop\\\\n\\\\t\\\\t\\\\t\\\\ton:end\\\\n\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\tmirror={webcam_options.mirror && active_source === \\\\\"webcam\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t\\\\t\\\\t{handle_change}\\\\n\\\\t\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t\\\\t{loop}\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t{show_download_button}\\\\n\\\\t\\\\t\\\\t\\\\t{handle_clear}\\\\n\\\\t\\\\t\\\\t\\\\t{has_change_history}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/key}\\\\n\\\\t{:else if value.size}\\\\n\\\\t\\\\t<div class=\\\\\"file-name\\\\\">{value.orig_name || value.url}</div>\\\\n\\\\t\\\\t<div class=\\\\\"file-size\\\\\">\\\\n\\\\t\\\\t\\\\t{prettyBytes(value.size)}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t<SelectSource {sources} bind:active_source {handle_clear} />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.file-name {\\\\n\\\\t\\\\tpadding: var(--size-6);\\\\n\\\\t\\\\tfont-size: var(--text-xxl);\\\\n\\\\t\\\\tword-break: break-all;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-size {\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\tfont-size: var(--text-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.upload-container {\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.video-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA4HC,yBAAW,CACV,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,UAAU,CAAC,CAC1B,UAAU,CAAE,SACb,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,gCAAkB,CACjB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd\"}'\n};\nconst InteractiveVideo = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value = null } = $$props;\n  let { subtitle = null } = $$props;\n  let { sources = [\"webcam\", \"upload\"] } = $$props;\n  let { label = void 0 } = $$props;\n  let { show_download_button = false } = $$props;\n  let { show_label = true } = $$props;\n  let { webcam_options } = $$props;\n  let { include_audio } = $$props;\n  let { autoplay } = $$props;\n  let { root } = $$props;\n  let { i18n } = $$props;\n  let { active_source = \"webcam\" } = $$props;\n  let { handle_reset_value = () => {\n  } } = $$props;\n  let { max_file_size = null } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let { loop } = $$props;\n  let { uploading = false } = $$props;\n  let has_change_history = false;\n  const dispatch = createEventDispatcher();\n  function handle_clear() {\n    value = null;\n    dispatch(\"change\", null);\n    dispatch(\"clear\");\n  }\n  function handle_change(video) {\n    has_change_history = true;\n    dispatch(\"change\", video);\n  }\n  let dragging = false;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.subtitle === void 0 && $$bindings.subtitle && subtitle !== void 0)\n    $$bindings.subtitle(subtitle);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.webcam_options === void 0 && $$bindings.webcam_options && webcam_options !== void 0)\n    $$bindings.webcam_options(webcam_options);\n  if ($$props.include_audio === void 0 && $$bindings.include_audio && include_audio !== void 0)\n    $$bindings.include_audio(include_audio);\n  if ($$props.autoplay === void 0 && $$bindings.autoplay && autoplay !== void 0)\n    $$bindings.autoplay(autoplay);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.active_source === void 0 && $$bindings.active_source && active_source !== void 0)\n    $$bindings.active_source(active_source);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.uploading === void 0 && $$bindings.uploading && uploading !== void 0)\n    $$bindings.uploading(uploading);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      dispatch(\"drag\", dragging);\n    }\n    $$rendered = `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n      $$result,\n      {\n        show_label,\n        Icon: Video$1,\n        label: label || \"Video\"\n      },\n      {},\n      {}\n    )} <div data-testid=\"video\" class=\"video-container svelte-14jis2k\">${value === null || value.url === void 0 ? `<div class=\"upload-container svelte-14jis2k\">${active_source === \"upload\" ? `${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        filetype: \"video/x-m4v,video/*\",\n        max_file_size,\n        root,\n        upload,\n        stream_handler,\n        aria_label: i18n(\"video.drop_to_upload\"),\n        dragging,\n        uploading\n      },\n      {\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        },\n        uploading: ($$value) => {\n          uploading = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${slots.default ? slots.default({}) : ``}`;\n        }\n      }\n    )}` : `${active_source === \"webcam\" ? `${validate_component(Webcam, \"Webcam\").$$render(\n      $$result,\n      {\n        root,\n        mirror_webcam: webcam_options.mirror,\n        webcam_constraints: webcam_options.constraints,\n        include_audio,\n        mode: \"video\",\n        i18n,\n        upload,\n        stream_every: 1\n      },\n      {},\n      {}\n    )}` : ``}`}</div>` : `${value?.url ? `${validate_component(Player, \"Player\").$$render(\n      $$result,\n      {\n        upload,\n        root,\n        interactive: true,\n        autoplay,\n        src: value.url,\n        subtitle: subtitle?.url,\n        is_stream: false,\n        mirror: webcam_options.mirror && active_source === \"webcam\",\n        label,\n        handle_change,\n        handle_reset_value,\n        loop,\n        value,\n        i18n,\n        show_download_button,\n        handle_clear,\n        has_change_history\n      },\n      {},\n      {}\n    )}` : `${value.size ? `<div class=\"file-name svelte-14jis2k\">${escape(value.orig_name || value.url)}</div> <div class=\"file-size svelte-14jis2k\">${escape(prettyBytes(value.size))}</div>` : ``}`}`} ${validate_component(SelectSource, \"SelectSource\").$$render(\n      $$result,\n      { sources, handle_clear, active_source },\n      {\n        active_source: ($$value) => {\n          active_source = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Video = InteractiveVideo;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = null } = $$props;\n  let old_value = null;\n  let { label } = $$props;\n  let { sources } = $$props;\n  let { root } = $$props;\n  let { show_label } = $$props;\n  let { loading_status } = $$props;\n  let { height } = $$props;\n  let { width } = $$props;\n  let { container = false } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { autoplay = false } = $$props;\n  let { show_share_button = true } = $$props;\n  let { show_download_button } = $$props;\n  let { gradio } = $$props;\n  let { interactive } = $$props;\n  let { webcam_options } = $$props;\n  let { include_audio } = $$props;\n  let { loop = false } = $$props;\n  let { input_ready } = $$props;\n  let uploading = false;\n  let _video = null;\n  let _subtitle = null;\n  let active_source;\n  let initial_value = value;\n  const handle_reset_value = () => {\n    if (initial_value === null || value === initial_value) {\n      return;\n    }\n    value = initial_value;\n  };\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.width === void 0 && $$bindings.width && width !== void 0)\n    $$bindings.width(width);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.autoplay === void 0 && $$bindings.autoplay && autoplay !== void 0)\n    $$bindings.autoplay(autoplay);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.webcam_options === void 0 && $$bindings.webcam_options && webcam_options !== void 0)\n    $$bindings.webcam_options(webcam_options);\n  if ($$props.include_audio === void 0 && $$bindings.include_audio && include_audio !== void 0)\n    $$bindings.include_audio(include_audio);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.input_ready === void 0 && $$bindings.input_ready && input_ready !== void 0)\n    $$bindings.input_ready(input_ready);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    input_ready = !uploading;\n    {\n      if (value && initial_value === null) {\n        initial_value = value;\n      }\n    }\n    {\n      if (sources && !active_source) {\n        active_source = sources[0];\n      }\n    }\n    {\n      {\n        if (value != null) {\n          _video = value.video;\n          _subtitle = value.subtitles;\n        } else {\n          _video = null;\n          _subtitle = null;\n        }\n      }\n    }\n    {\n      {\n        if (JSON.stringify(value) !== JSON.stringify(old_value)) {\n          old_value = value;\n          gradio.dispatch(\"change\");\n        }\n      }\n    }\n    $$rendered = `  ${!interactive ? `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: value === null && active_source === \"upload\" ? \"dashed\" : \"solid\",\n        border_mode: \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        height,\n        width,\n        container,\n        scale,\n        min_width,\n        allow_overflow: false\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(VideoPreview, \"StaticVideo\").$$render(\n            $$result,\n            {\n              value: _video,\n              subtitle: _subtitle,\n              label,\n              show_label,\n              autoplay,\n              loop,\n              show_share_button,\n              show_download_button,\n              i18n: gradio.i18n,\n              upload: (...args) => gradio.client.upload(...args)\n            },\n            {},\n            {}\n          )}`;\n        }\n      }\n    )}` : `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: value === null && active_source === \"upload\" ? \"dashed\" : \"solid\",\n        border_mode: \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        height,\n        width,\n        container,\n        scale,\n        min_width,\n        allow_overflow: false\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(Video, \"Video\").$$render(\n            $$result,\n            {\n              value: _video,\n              subtitle: _subtitle,\n              label,\n              show_label,\n              show_download_button,\n              sources,\n              active_source,\n              webcam_options,\n              include_audio,\n              autoplay,\n              root,\n              loop,\n              handle_reset_value,\n              i18n: gradio.i18n,\n              max_file_size: gradio.max_file_size,\n              upload: (...args) => gradio.client.upload(...args),\n              stream_handler: (...args) => gradio.client.stream(...args),\n              uploading\n            },\n            {\n              uploading: ($$value) => {\n                uploading = $$value;\n                $$settled = false;\n              }\n            },\n            {\n              default: () => {\n                return `${validate_component(UploadText, \"UploadText\").$$render($$result, { i18n: gradio.i18n, type: \"video\" }, {}, {})}`;\n              }\n            }\n          )}`;\n        }\n      }\n    )}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Index$1 = Index;\nexport {\n  default2 as BaseExample,\n  Video as BaseInteractiveVideo,\n  Player as BasePlayer,\n  VideoPreview as BaseStaticVideo,\n  Index$1 as default,\n  l as loaded,\n  a as playable,\n  prettyBytes\n};\n"], "names": ["Webcam", "Player"], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2VAA2V;AACnW,EAAE,GAAG,EAAE,8xJAA8xJ;AACryJ,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACxF,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACnD,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,aAAa,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,kBAAkB,GAAG,MAAM;AACnC,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACjC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC7B,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,kBAAkB,GAAG,IAAI,CAAC;AAC9B,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,KAAK,EAAE,KAAK,IAAI,OAAO;AAC/B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,iEAAiE,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,6CAA6C,EAAE,aAAa,KAAK,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC/O,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ,EAAE,qBAAqB;AACvC,QAAQ,aAAa;AACrB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC;AAChD,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,aAAa,KAAK,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC1F,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,aAAa,EAAE,cAAc,CAAC,MAAM;AAC5C,QAAQ,kBAAkB,EAAE,cAAc,CAAC,WAAW;AACtD,QAAQ,aAAa;AACrB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,YAAY,EAAE,CAAC;AACvB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE,kBAAkB,CAACC,QAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AACzF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,QAAQ;AAChB,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG;AACtB,QAAQ,QAAQ,EAAE,QAAQ,EAAE,GAAG;AAC/B,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,aAAa,KAAK,QAAQ;AACnE,QAAQ,KAAK;AACb,QAAQ,aAAa;AACrB,QAAQ,kBAAkB;AAC1B,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,oBAAoB;AAC5B,QAAQ,YAAY;AACpB,QAAQ,kBAAkB;AAC1B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,sCAAsC,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,6CAA6C,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACpQ,MAAM,QAAQ;AACd,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE;AAC9C,MAAM;AACN,QAAQ,aAAa,EAAE,CAAC,OAAO,KAAK;AACpC,UAAU,aAAa,GAAG,OAAO,CAAC;AAClC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,KAAK,GAAG,iBAAiB;AAC/B,MAAM,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B,EAAE,MAAM,kBAAkB,GAAG,MAAM;AACnC,IAAI,IAAI,aAAa,KAAK,IAAI,IAAI,KAAK,KAAK,aAAa,EAAE;AAC3D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,KAAK,GAAG,aAAa,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,WAAW,GAAG,CAAC,SAAS,CAAC;AAC7B,IAAI;AACJ,MAAM,IAAI,KAAK,IAAI,aAAa,KAAK,IAAI,EAAE;AAC3C,QAAQ,aAAa,GAAG,KAAK,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AACrC,QAAQ,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;AAC3B,UAAU,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AACtC,SAAS,MAAM;AACf,UAAU,MAAM,GAAG,IAAI,CAAC;AACxB,UAAU,SAAS,GAAG,IAAI,CAAC;AAC3B,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACjE,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACnF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,KAAK,KAAK,IAAI,IAAI,aAAa,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;AAClF,QAAQ,WAAW,EAAE,MAAM;AAC3B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,cAAc,EAAE,KAAK;AAC7B,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,QAAQ;AACnP,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,KAAK,EAAE,MAAM;AAC3B,cAAc,QAAQ,EAAE,SAAS;AACjC,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,QAAQ;AACtB,cAAc,IAAI;AAClB,cAAc,iBAAiB;AAC/B,cAAc,oBAAoB;AAClC,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAChE,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxD,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,KAAK,KAAK,IAAI,IAAI,aAAa,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;AAClF,QAAQ,WAAW,EAAE,MAAM;AAC3B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,cAAc,EAAE,KAAK;AAC7B,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACtO,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,KAAK,EAAE,MAAM;AAC3B,cAAc,QAAQ,EAAE,SAAS;AACjC,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,oBAAoB;AAClC,cAAc,OAAO;AACrB,cAAc,aAAa;AAC3B,cAAc,cAAc;AAC5B,cAAc,aAAa;AAC3B,cAAc,QAAQ;AACtB,cAAc,IAAI;AAClB,cAAc,IAAI;AAClB,cAAc,kBAAkB;AAChC,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,aAAa,EAAE,MAAM,CAAC,aAAa;AACjD,cAAc,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAChE,cAAc,cAAc,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACxE,cAAc,SAAS;AACvB,aAAa;AACb,YAAY;AACZ,cAAc,SAAS,EAAE,CAAC,OAAO,KAAK;AACtC,gBAAgB,SAAS,GAAG,OAAO,CAAC;AACpC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,MAAM;AAC7B,gBAAgB,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1I,eAAe;AACf,aAAa;AACb,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,OAAO,GAAG;;;;"}