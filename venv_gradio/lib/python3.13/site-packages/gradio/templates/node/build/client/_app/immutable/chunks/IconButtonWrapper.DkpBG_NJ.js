import { SvelteComponent, init, safe_not_equal, create_slot, element, claim_element, children, detach, attr, null_to_empty, insert_hydration, update_slot_base, get_all_dirty_from_scope, get_slot_changes, transition_in, transition_out } from "../../../svelte/svelte.js";
import "../../../svelte/svelte-submodules.js";
import "./2.CWSJHAMp.js";
function create_fragment(ctx) {
  let div;
  let div_class_value;
  let current;
  const default_slot_template = (
    /*#slots*/
    ctx[3].default
  );
  const default_slot = create_slot(
    default_slot_template,
    ctx,
    /*$$scope*/
    ctx[2],
    null
  );
  return {
    c() {
      div = element("div");
      if (default_slot)
        default_slot.c();
      this.h();
    },
    l(nodes) {
      div = claim_element(nodes, "DIV", { class: true });
      var div_nodes = children(div);
      if (default_slot)
        default_slot.l(div_nodes);
      div_nodes.forEach(detach);
      this.h();
    },
    h() {
      attr(div, "class", div_class_value = null_to_empty(`icon-button-wrapper ${/*top_panel*/
      ctx[0] ? "top-panel" : ""} ${/*display_top_corner*/
      ctx[1] ? "display-top-corner" : "hide-top-corner"}`) + " svelte-9lsba8");
    },
    m(target, anchor) {
      insert_hydration(target, div, anchor);
      if (default_slot) {
        default_slot.m(div, null);
      }
      current = true;
    },
    p(ctx2, [dirty]) {
      if (default_slot) {
        if (default_slot.p && (!current || dirty & /*$$scope*/
        4)) {
          update_slot_base(
            default_slot,
            default_slot_template,
            ctx2,
            /*$$scope*/
            ctx2[2],
            !current ? get_all_dirty_from_scope(
              /*$$scope*/
              ctx2[2]
            ) : get_slot_changes(
              default_slot_template,
              /*$$scope*/
              ctx2[2],
              dirty,
              null
            ),
            null
          );
        }
      }
      if (!current || dirty & /*top_panel, display_top_corner*/
      3 && div_class_value !== (div_class_value = null_to_empty(`icon-button-wrapper ${/*top_panel*/
      ctx2[0] ? "top-panel" : ""} ${/*display_top_corner*/
      ctx2[1] ? "display-top-corner" : "hide-top-corner"}`) + " svelte-9lsba8")) {
        attr(div, "class", div_class_value);
      }
    },
    i(local) {
      if (current)
        return;
      transition_in(default_slot, local);
      current = true;
    },
    o(local) {
      transition_out(default_slot, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      if (default_slot)
        default_slot.d(detaching);
    }
  };
}
function instance($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  let { top_panel = true } = $$props;
  let { display_top_corner = false } = $$props;
  $$self.$$set = ($$props2) => {
    if ("top_panel" in $$props2)
      $$invalidate(0, top_panel = $$props2.top_panel);
    if ("display_top_corner" in $$props2)
      $$invalidate(1, display_top_corner = $$props2.display_top_corner);
    if ("$$scope" in $$props2)
      $$invalidate(2, $$scope = $$props2.$$scope);
  };
  return [top_panel, display_top_corner, $$scope, slots];
}
class IconButtonWrapper extends SvelteComponent {
  constructor(options) {
    super();
    init(this, options, instance, create_fragment, safe_not_equal, { top_panel: 0, display_top_corner: 1 });
  }
}
export {
  IconButtonWrapper as I
};
