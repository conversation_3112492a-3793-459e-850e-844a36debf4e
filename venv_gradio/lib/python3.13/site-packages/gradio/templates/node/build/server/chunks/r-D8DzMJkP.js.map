{"version": 3, "file": "r-D8DzMJkP.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/r.js"], "sourcesContent": ["function wordObj(words) {\n  var res = {};\n  for (var i = 0; i < words.length; ++i)\n    res[words[i]] = true;\n  return res;\n}\nvar commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\nvar commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\nvar commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\nvar commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\nvar atoms = wordObj(commonAtoms);\nvar builtins = wordObj(commonBuiltins);\nvar keywords = wordObj(commonKeywords);\nvar blockkeywords = wordObj(commonBlockKeywords);\nvar opChars = /[+\\-*\\/^<>=!&|~$:]/;\nvar curPunc;\nfunction tokenBase(stream, state) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"0\" && stream.eat(\"x\")) {\n    stream.eatWhile(/[\\da-f]/i);\n    return \"number\";\n  } else if (ch == \".\" && stream.eat(/\\d/)) {\n    stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n    return \"number\";\n  } else if (ch == \"'\" || ch == '\"') {\n    state.tokenize = tokenString(ch);\n    return \"string\";\n  } else if (ch == \"`\") {\n    stream.match(/[^`]+`/);\n    return \"string.special\";\n  } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n    return \"keyword\";\n  } else if (/[a-zA-Z\\.]/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    var word = stream.current();\n    if (atoms.propertyIsEnumerable(word))\n      return \"atom\";\n    if (keywords.propertyIsEnumerable(word)) {\n      if (blockkeywords.propertyIsEnumerable(word) && !stream.match(/\\s*if(\\s+|$)/, false))\n        curPunc = \"block\";\n      return \"keyword\";\n    }\n    if (builtins.propertyIsEnumerable(word))\n      return \"builtin\";\n    return \"variable\";\n  } else if (ch == \"%\") {\n    if (stream.skipTo(\"%\"))\n      stream.next();\n    return \"variableName.special\";\n  } else if (ch == \"<\" && stream.eat(\"-\") || ch == \"<\" && stream.match(\"<-\") || ch == \"-\" && stream.match(/>>?/)) {\n    return \"operator\";\n  } else if (ch == \"=\" && state.ctx.argList) {\n    return \"operator\";\n  } else if (opChars.test(ch)) {\n    if (ch == \"$\")\n      return \"operator\";\n    stream.eatWhile(opChars);\n    return \"operator\";\n  } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n    curPunc = ch;\n    if (ch == \";\")\n      return \"punctuation\";\n    return null;\n  } else {\n    return null;\n  }\n}\nfunction tokenString(quote) {\n  return function(stream, state) {\n    if (stream.eat(\"\\\\\")) {\n      var ch = stream.next();\n      if (ch == \"x\")\n        stream.match(/^[a-f0-9]{2}/i);\n      else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\"))\n        stream.next();\n      else if (ch == \"u\")\n        stream.match(/^[a-f0-9]{4}/i);\n      else if (ch == \"U\")\n        stream.match(/^[a-f0-9]{8}/i);\n      else if (/[0-7]/.test(ch))\n        stream.match(/^[0-7]{1,2}/);\n      return \"string.special\";\n    } else {\n      var next;\n      while ((next = stream.next()) != null) {\n        if (next == quote) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        if (next == \"\\\\\") {\n          stream.backUp(1);\n          break;\n        }\n      }\n      return \"string\";\n    }\n  };\n}\nvar ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4;\nfunction push(state, type, stream) {\n  state.ctx = {\n    type,\n    indent: state.indent,\n    flags: 0,\n    column: stream.column(),\n    prev: state.ctx\n  };\n}\nfunction setFlag(state, flag) {\n  var ctx = state.ctx;\n  state.ctx = {\n    type: ctx.type,\n    indent: ctx.indent,\n    flags: ctx.flags | flag,\n    column: ctx.column,\n    prev: ctx.prev\n  };\n}\nfunction pop(state) {\n  state.indent = state.ctx.indent;\n  state.ctx = state.ctx.prev;\n}\nconst r = {\n  name: \"r\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: tokenBase,\n      ctx: {\n        type: \"top\",\n        indent: -indentUnit,\n        flags: ALIGN_NO\n      },\n      indent: 0,\n      afterIdent: false\n    };\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if ((state.ctx.flags & 3) == 0)\n        state.ctx.flags |= ALIGN_NO;\n      if (state.ctx.flags & BRACELESS)\n        pop(state);\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace())\n      return null;\n    var style = state.tokenize(stream, state);\n    if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0)\n      setFlag(state, ALIGN_YES);\n    if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\")\n      pop(state);\n    if (curPunc == \"{\")\n      push(state, \"}\", stream);\n    else if (curPunc == \"(\") {\n      push(state, \")\", stream);\n      if (state.afterIdent)\n        state.ctx.argList = true;\n    } else if (curPunc == \"[\")\n      push(state, \"]\", stream);\n    else if (curPunc == \"block\")\n      push(state, \"block\", stream);\n    else if (curPunc == state.ctx.type)\n      pop(state);\n    else if (state.ctx.type == \"block\" && style != \"comment\")\n      setFlag(state, BRACELESS);\n    state.afterIdent = style == \"variable\" || style == \"keyword\";\n    return style;\n  },\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase)\n      return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx, closing = firstChar == ctx.type;\n    if (ctx.flags & BRACELESS)\n      ctx = ctx.prev;\n    if (ctx.type == \"block\")\n      return ctx.indent + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.flags & ALIGN_YES)\n      return ctx.column + (closing ? 0 : 1);\n    else\n      return ctx.indent + (closing ? 0 : cx.unit);\n  },\n  languageData: {\n    wordChars: \".\",\n    commentTokens: { line: \"#\" },\n    autocomplete: commonAtoms.concat(commonBuiltins, commonKeywords)\n  }\n};\nexport {\n  r\n};\n"], "names": [], "mappings": "AAAA,SAAS,OAAO,CAAC,KAAK,EAAE;AACxB,EAAE,IAAI,GAAG,GAAG,EAAE,CAAC;AACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;AACvC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACzB,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3H,IAAI,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AAC/F,IAAI,cAAc,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACjG,IAAI,mBAAmB,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAC/E,IAAI,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AACjC,IAAI,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AACvC,IAAI,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AACvC,IAAI,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACjD,IAAI,OAAO,GAAG,oBAAoB,CAAC;AACnC,IAAI,OAAO,CAAC;AACZ,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;AAClC,EAAE,OAAO,GAAG,IAAI,CAAC;AACjB,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE;AACjB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;AACvB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC3C,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAChC,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC5C,IAAI,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;AACvC,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAClD,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACrC,IAAI,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AACrC,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AACxB,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC3B,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;AACxD,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACpC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9B,IAAI,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AAChC,IAAI,IAAI,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC;AACxC,MAAM,OAAO,MAAM,CAAC;AACpB,IAAI,IAAI,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAC7C,MAAM,IAAI,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC;AAC1F,QAAQ,OAAO,GAAG,OAAO,CAAC;AAC1B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC;AAC3C,MAAM,OAAO,SAAS,CAAC;AACvB,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AACxB,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;AAC1B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,IAAI,OAAO,sBAAsB,CAAC;AAClC,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAClH,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;AAC7C,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/B,IAAI,IAAI,EAAE,IAAI,GAAG;AACjB,MAAM,OAAO,UAAU,CAAC;AACxB,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC7B,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,MAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACvC,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,EAAE,IAAI,GAAG;AACjB,MAAM,OAAO,aAAa,CAAC;AAC3B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM;AACT,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC1B,MAAM,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAC7B,MAAM,IAAI,EAAE,IAAI,GAAG;AACnB,QAAQ,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACtC,WAAW,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;AAChF,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,WAAW,IAAI,EAAE,IAAI,GAAG;AACxB,QAAQ,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACtC,WAAW,IAAI,EAAE,IAAI,GAAG;AACxB,QAAQ,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACtC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;AAC/B,QAAQ,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACpC,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK,MAAM;AACX,MAAM,IAAI,IAAI,CAAC;AACf,MAAM,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC7C,QAAQ,IAAI,IAAI,IAAI,KAAK,EAAE;AAC3B,UAAU,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC;AACrC,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,UAAU,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3B,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AAC/C,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;AACnC,EAAE,KAAK,CAAC,GAAG,GAAG;AACd,IAAI,IAAI;AACR,IAAI,MAAM,EAAE,KAAK,CAAC,MAAM;AACxB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;AAC3B,IAAI,IAAI,EAAE,KAAK,CAAC,GAAG;AACnB,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;AAC9B,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACtB,EAAE,KAAK,CAAC,GAAG,GAAG;AACd,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI;AAClB,IAAI,MAAM,EAAE,GAAG,CAAC,MAAM;AACtB,IAAI,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI;AAC3B,IAAI,MAAM,EAAE,GAAG,CAAC,MAAM;AACtB,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI;AAClB,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,GAAG,CAAC,KAAK,EAAE;AACpB,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;AAClC,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AAC7B,CAAC;AACI,MAAC,CAAC,GAAG;AACV,EAAE,IAAI,EAAE,GAAG;AACX,EAAE,UAAU,EAAE,SAAS,UAAU,EAAE;AACnC,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,GAAG,EAAE;AACX,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,MAAM,EAAE,CAAC,UAAU;AAC3B,QAAQ,KAAK,EAAE,QAAQ;AACvB,OAAO;AACP,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,UAAU,EAAE,KAAK;AACvB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE;AACtB,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC;AACpC,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,QAAQ,CAAC;AACpC,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,SAAS;AACrC,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC;AACnB,MAAM,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC9C,IAAI,IAAI,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,KAAK,CAAC;AAC/D,MAAM,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO;AACzF,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AACjB,IAAI,IAAI,OAAO,IAAI,GAAG;AACtB,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/B,SAAS,IAAI,OAAO,IAAI,GAAG,EAAE;AAC7B,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/B,MAAM,IAAI,KAAK,CAAC,UAAU;AAC1B,QAAQ,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;AACjC,KAAK,MAAM,IAAI,OAAO,IAAI,GAAG;AAC7B,MAAM,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/B,SAAS,IAAI,OAAO,IAAI,OAAO;AAC/B,MAAM,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACnC,SAAS,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI;AACtC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AACjB,SAAS,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,SAAS;AAC5D,MAAM,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChC,IAAI,KAAK,CAAC,UAAU,GAAG,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,SAAS,CAAC;AACjE,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE;AACzC,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,SAAS;AACnC,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,IAAI,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,GAAG,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC;AACvG,IAAI,IAAI,GAAG,CAAC,KAAK,GAAG,SAAS;AAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC;AACrB,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO;AAC3B,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,SAAS,IAAI,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AAC3D,SAAS,IAAI,GAAG,CAAC,KAAK,GAAG,SAAS;AAClC,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,SAAS,EAAE,GAAG;AAClB,IAAI,aAAa,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,IAAI,YAAY,EAAE,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC;AACpE,GAAG;AACH;;;;"}