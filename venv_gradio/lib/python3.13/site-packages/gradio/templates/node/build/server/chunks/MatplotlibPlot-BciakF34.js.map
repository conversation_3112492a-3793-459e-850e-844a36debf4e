{"version": 3, "file": "MatplotlibPlot-BciakF34.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/MatplotlibPlot.js"], "sourcesContent": ["import { create_ssr_component, add_attribute } from \"svelte/internal\";\nconst css = {\n  code: \".layout.svelte-j1jcu3.svelte-j1jcu3{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);color:var(--body-text-color)}.matplotlib.svelte-j1jcu3 img.svelte-j1jcu3{object-fit:contain}\",\n  map: '{\"version\":3,\"file\":\"MatplotlibPlot.svelte\",\"sources\":[\"MatplotlibPlot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\n$: plot = value?.plot;\\\\n<\\/script>\\\\n\\\\n<div data-testid={\\\\\"matplotlib\\\\\"} class=\\\\\"matplotlib layout\\\\\">\\\\n\\\\t<img\\\\n\\\\t\\\\tsrc={plot}\\\\n\\\\t\\\\talt={`${value.chart} plot visualising provided data`}\\\\n\\\\t\\\\ton:load\\\\n\\\\t/>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.layout {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\t.matplotlib img {\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAaC,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CACA,yBAAW,CAAC,iBAAI,CACf,UAAU,CAAE,OACb\"}'\n};\nconst MatplotlibPlot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let plot;\n  let { value } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  $$result.css.add(css);\n  plot = value?.plot;\n  return `<div${add_attribute(\"data-testid\", \"matplotlib\", 0)} class=\"matplotlib layout svelte-j1jcu3\"><img${add_attribute(\"src\", plot, 0)}${add_attribute(\"alt\", `${value.chart} plot visualising provided data`, 0)} class=\"svelte-j1jcu3\"> </div>`;\n});\nexport {\n  MatplotlibPlot as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,8PAA8P;AACtQ,EAAE,GAAG,EAAE,g8BAAg8B;AACv8B,CAAC,CAAC;AACG,MAAC,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;AACrB,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,6CAA6C,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACtP,CAAC;;;;"}