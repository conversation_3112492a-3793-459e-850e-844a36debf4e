{"version": 3, "file": "Example13-BxKMoTXm.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example13.js"], "sourcesContent": ["import { create_ssr_component, each, escape } from \"svelte/internal\";\nconst css = {\n  code: \"ul.svelte-4tf8f{white-space:nowrap;max-height:100px;list-style:none;padding:0;margin:0}.extra.svelte-4tf8f{text-align:center}.gallery.svelte-4tf8f{align-items:center;cursor:pointer;padding:var(--size-1) var(--size-2);text-align:left}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<ul\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n>\\\\n\\\\t{#if value}\\\\n\\\\t\\\\t{#each Array.isArray(value) ? value.slice(0, 3) : [value] as path}\\\\n\\\\t\\\\t\\\\t<li><code>./{path}</code></li>\\\\n\\\\t\\\\t{/each}\\\\n\\\\t\\\\t{#if Array.isArray(value) && value.length > 3}\\\\n\\\\t\\\\t\\\\t<li class=\\\\\"extra\\\\\">...</li>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{/if}\\\\n</ul>\\\\n\\\\n<style>\\\\n\\\\tul {\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\tmax-height: 100px;\\\\n\\\\t\\\\tlist-style: none;\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.extra {\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery {\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqBC,eAAG,CACF,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CACT,CAEA,mBAAO,CACN,UAAU,CAAE,MACb,CAEA,qBAAS,CACR,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,UAAU,CAAE,IACb\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<ul class=\"${[\n    \"svelte-4tf8f\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\">${value ? `${each(Array.isArray(value) ? value.slice(0, 3) : [value], (path) => {\n    return `<li><code>./${escape(path)}</code></li>`;\n  })} ${Array.isArray(value) && value.length > 3 ? `<li class=\"extra svelte-4tf8f\" data-svelte-h=\"svelte-17d9ayl\">...</li>` : ``}` : ``} </ul>`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2OAA2O;AACnP,EAAE,GAAG,EAAE,+tCAA+tC;AACtuC,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,cAAc;AAClB,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK;AACvG,IAAI,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,sEAAsE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAChJ,CAAC;;;;"}