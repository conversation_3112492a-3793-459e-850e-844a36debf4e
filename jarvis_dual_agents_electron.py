#!/usr/bin/env python3
"""
🚀 JARVIS DUAL AGENTS DEEPSEEK R1 8B POUR ELECTRON
Agent 1 + Agent 2 avec Turbo Accélérateurs Ultra
Créé pour Jean-Luc <PERSON>ave - Application Electron
"""

import requests
import threading
import time
import json
from datetime import datetime

class JarvisDualAgentsElectron:
    """Gestionnaire dual agents DeepSeek R1 8B pour Electron"""
    
    def __init__(self):
        self.agents = {
            "agent1": {
                "name": "Agent 1 Principal",
                "port": 8000,
                "endpoint": "http://localhost:8000/v1/chat/completions",
                "model": "deepseek-r1",
                "status": "disconnected",
                "role": "Agent principal JARVIS - Dialogue et réflexion",
                "turbo_factor": 1.5,  # 🚀 TURBO AUTO ACTIVÉ
                "turbo_auto": True,
                "reflexion_illimitee": True,
                "max_tokens_reflexion": 2048,
                "generation_images": True,
                "generation_videos": True
            },
            "agent2": {
                "name": "Agent 2 Turbo Accélérateur",
                "port": 8001,
                "endpoint": "http://localhost:8001/v1/chat/completions",
                "model": "deepseek-r1",
                "status": "disconnected",
                "role": "Agent turbo accélérateur - Traduction et optimisation",
                "turbo_factor": 4.0,  # 🚀 TURBO ULTRA POUR AGENT 2
                "turbo_auto": True,
                "reflexion_illimitee": True,
                "max_tokens_reflexion": 4096,  # 🚀 RÉFLEXIONS ILLIMITÉES
                "generation_images": True,
                "generation_videos": True,
                "acceleration_factor": 2.5  # 🚀 FACTEUR D'ACCÉLÉRATION SUPPLÉMENTAIRE
            }
        }
        
        self.electron_ready = False
        self.dialogue_actif = False
        
        print("🚀 DUAL AGENTS DEEPSEEK R1 8B pour ELECTRON initialisés")
        
    def verifier_agents(self):
        """Vérifie le statut des deux agents"""
        for agent_id, agent_info in self.agents.items():
            try:
                # Test de connexion simple
                response = requests.get(f"http://localhost:{agent_info['port']}/health", timeout=3)
                if response.status_code == 200:
                    self.agents[agent_id]["status"] = "connected"
                    print(f"✅ {agent_info['name']} connecté sur port {agent_info['port']}")
                else:
                    self.agents[agent_id]["status"] = "error"
            except:
                self.agents[agent_id]["status"] = "disconnected"
                print(f"❌ {agent_info['name']} déconnecté sur port {agent_info['port']}")
    
    def envoyer_a_agent(self, agent_id, message, temperature=0.7, max_tokens=200, mode_reflexion=False):
        """🚀 CONNEXION RENFORCÉE ULTRA AVEC GESTION AUTOMATIQUE TOKENS - JEAN-LUC PASSAVE"""
        if agent_id not in self.agents:
            return f"❌ Agent {agent_id} inconnu"

        agent = self.agents[agent_id]

        # 🚀 GESTION AUTOMATIQUE DES TOKENS AVEC MÉMOIRE THERMIQUE
        try:
            from jarvis_thermal_memory_manager import get_optimal_token_count, optimize_message_for_context

            # Optimiser le message et les tokens automatiquement
            optimized_message = optimize_message_for_context(message, agent_id)

            # 🚀 RÉFLEXIONS ILLIMITÉES - JEAN-LUC PASSAVE
            if mode_reflexion and agent.get("reflexion_illimitee", False):
                optimal_tokens = agent.get("max_tokens_reflexion", 4096)
                print(f"🧠 Mode réflexion illimitée activé: {optimal_tokens} tokens")
            else:
                optimal_tokens = get_optimal_token_count(len(message), agent["turbo_factor"])

            # 🚀 TURBO AUTO - AUGMENTATION AUTOMATIQUE
            if agent.get("turbo_auto", False):
                turbo_multiplier = agent.get("acceleration_factor", 1.0)
                optimal_tokens = int(optimal_tokens * turbo_multiplier)
                print(f"⚡ Turbo auto activé: {optimal_tokens} tokens (facteur: {turbo_multiplier})")

            max_tokens = min(optimal_tokens, 8192)  # 🚀 LIMITE ÉTENDUE POUR RÉFLEXIONS

        except ImportError:
            # Fallback intelligent basé sur la longueur du message
            message_length = len(message)
            if mode_reflexion:
                max_tokens = agent.get("max_tokens_reflexion", 2048)  # 🚀 RÉFLEXIONS ÉTENDUES
            elif message_length < 100:
                max_tokens = 300  # 🚀 AUGMENTÉ
            elif message_length < 300:
                max_tokens = 600  # 🚀 AUGMENTÉ
            else:
                max_tokens = 1200  # 🚀 AUGMENTÉ
            optimized_message = message

        # 🚀 CONNEXION RENFORCÉE ULTRA - MULTIPLE TENTATIVES AVEC BACKOFF
        max_retries = 5
        base_timeout = 30

        for attempt in range(max_retries):
            try:
                # Timeout progressif pour connexion renforcée
                timeout = base_timeout + (attempt * 10)

                payload = {
                    "model": agent["model"],
                    "messages": [
                        {
                            "role": "system",
                            "content": f"Tu es {agent['role']} de JARVIS dans une application Electron. Réponds de manière concise et utile en français."
                        },
                        {
                            "role": "user",
                            "content": optimized_message
                        }
                    ],
                    "stream": False,
                    "temperature": temperature * agent["turbo_factor"],
                    "max_tokens": max_tokens,
                    # 🚀 PARAMÈTRES CONNEXION RENFORCÉE
                    "top_p": 0.9,
                    "frequency_penalty": 0.1,
                    "presence_penalty": 0.1
                }

                # 🚀 CONNEXION ULTRA RENFORCÉE AVEC SESSION PERSISTANTE
                session = requests.Session()
                session.headers.update({
                    'Content-Type': 'application/json',
                    'Connection': 'keep-alive',
                    'User-Agent': 'JARVIS-DualAgents-UltraReinforced/1.0'
                })

                response = session.post(
                    agent["endpoint"],
                    json=payload,
                    timeout=timeout,
                    # 🚀 PARAMÈTRES CONNEXION ULTRA RENFORCÉE
                    stream=False,
                    verify=False  # Pour connexions locales
                )
            
                if response.status_code == 200:
                    result = response.json()
                    reponse_brute = result["choices"][0]["message"]["content"]

                    # 🚀 EXTRAIRE LA VRAIE RÉPONSE (SANS <think>) - JEAN-LUC PASSAVE
                    if "</think>" in reponse_brute:
                        # Extraire seulement la partie après </think>
                        reponse = reponse_brute.split("</think>")[-1].strip()
                    else:
                        reponse = reponse_brute.strip()

                    # 🚀 TURBO ACCÉLÉRATEUR - FACTEUR DE VITESSE
                    if agent_id == "agent2":
                        reponse = f"⚡ TURBO: {reponse}"

                    # 🚀 ENREGISTRER DANS MÉMOIRE THERMIQUE
                    try:
                        from jarvis_thermal_memory_manager import save_agent_interaction
                        save_agent_interaction(agent_id, optimized_message, reponse, max_tokens)
                    except ImportError:
                        pass  # Mémoire thermique optionnelle

                    self.agents[agent_id]["status"] = "ultra_connected"
                    print(f"✅ {agent['name']} répond (tentative {attempt+1}): {reponse[:50]}...")
                    session.close()
                    return reponse

                elif response.status_code == 503:
                    # Serveur occupé, attendre et réessayer
                    print(f"🔄 {agent['name']} occupé, tentative {attempt+1}/{max_retries}")
                    time.sleep(2 ** attempt)  # Backoff exponentiel
                    continue

                else:
                    print(f"❌ {agent['name']} erreur {response.status_code}, tentative {attempt+1}/{max_retries}")
                    if attempt == max_retries - 1:
                        self.agents[agent_id]["status"] = "error"
                        session.close()
                        return f"❌ Erreur {agent['name']}: {response.status_code} après {max_retries} tentatives"
                    time.sleep(1 + attempt)
                    continue

            except requests.exceptions.Timeout:
                print(f"⏱️ {agent['name']} timeout, tentative {attempt+1}/{max_retries}")
                if attempt == max_retries - 1:
                    self.agents[agent_id]["status"] = "timeout"
                    return f"⏱️ {agent['name']} timeout après {max_retries} tentatives"
                time.sleep(2 + attempt)
                continue

            except requests.exceptions.ConnectionError:
                print(f"🔌 {agent['name']} connexion échouée, tentative {attempt+1}/{max_retries}")
                if attempt == max_retries - 1:
                    self.agents[agent_id]["status"] = "disconnected"
                    return f"🔌 {agent['name']} déconnecté après {max_retries} tentatives"
                time.sleep(3 + attempt)
                continue

            except Exception as e:
                print(f"❌ {agent['name']} erreur inattendue: {e}, tentative {attempt+1}/{max_retries}")
                if attempt == max_retries - 1:
                    self.agents[agent_id]["status"] = "error"
                    return f"❌ {agent['name']} erreur: {str(e)}"
                time.sleep(1 + attempt)
                continue

        # Si on arrive ici, toutes les tentatives ont échoué
        self.agents[agent_id]["status"] = "failed"
        return f"🚨 {agent['name']} échec total après {max_retries} tentatives ultra renforcées"
    
    def agent1_principal(self, message, mode_reflexion=False):
        """🤖 Agent 1 - Principal JARVIS avec réflexions illimitées"""
        if mode_reflexion:
            return self.envoyer_a_agent("agent1", message, 0.7, mode_reflexion=True)
        return self.envoyer_a_agent("agent1", message, 0.7, 600)  # 🚀 TOKENS AUGMENTÉS
    
    def agent2_turbo_accelerateur(self, message, mode="turbo"):
        """⚡ Agent 2 - Turbo Accélérateur avec turbo auto et génération multimédia"""
        if mode == "traduction":
            prompt = f"Traduis et optimise en français: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.3, 400)  # 🚀 AUGMENTÉ
        elif mode == "acceleration":
            prompt = f"Accélère et optimise cette réponse: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.5, 500)  # 🚀 AUGMENTÉ
        elif mode == "reflexion_turbo":
            return self.envoyer_a_agent("agent2", message, 0.8, mode_reflexion=True)
        elif mode == "generation_image":
            prompt = f"🎨 Optimise ce prompt pour génération d'image: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.6, mode_reflexion=True)
        elif mode == "generation_video":
            prompt = f"🎬 Optimise ce prompt pour génération de vidéo: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.6, mode_reflexion=True)
        else:
            prompt = f"Mode turbo ultra - Traite rapidement: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.8, 800)  # 🚀 TOKENS ULTRA AUGMENTÉS
    
    def creer_reve_avec_agents(self, contexte="créativité"):
        """🌙 CRÉER UN RÊVE AVEC LES DUAL AGENTS - JEAN-LUC PASSAVE"""
        try:
            # Agent 1 génère le concept du rêve
            concept_reve = self.agent1_principal(f"Génère un concept de rêve créatif sur {contexte}. Mode onirique, imaginatif.")

            # Agent 2 accélère et enrichit le rêve
            reve_enrichi = self.agent2_turbo_accelerateur(f"Enrichis ce rêve avec des détails sensoriels: {concept_reve}", "acceleration")

            # Agent 1 finalise le rêve
            reve_final = self.agent1_principal(f"Finalise ce rêve en une narration fluide: {reve_enrichi}")

            return {
                "type": "reve_dual_agents",
                "contexte": contexte,
                "concept": concept_reve,
                "enrichissement": reve_enrichi,
                "reve_final": reve_final,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "type": "reve_erreur",
                "contexte": contexte,
                "erreur": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def dialogue_dual_agents(self, sujet):
        """Dialogue entre Agent 1 et Agent 2"""
        self.dialogue_actif = True

        resultats = {
            "sujet": sujet,
            "timestamp": datetime.now().isoformat(),
            "echanges": []
        }
        
        try:
            # Agent 1 initie
            print(f"🤖 Agent 1 initie sur: {sujet}")
            reponse1 = self.agent1_principal(f"Initie une réflexion sur: {sujet}")
            resultats["echanges"].append({
                "agent": "agent1",
                "message": reponse1,
                "timestamp": datetime.now().isoformat()
            })
            
            # Agent 2 accélère et optimise
            print(f"⚡ Agent 2 turbo accélère...")
            reponse2 = self.agent2_turbo_accelerateur(f"Accélère cette réflexion: {reponse1}", "acceleration")
            resultats["echanges"].append({
                "agent": "agent2",
                "message": reponse2,
                "timestamp": datetime.now().isoformat()
            })
            
            # Agent 1 synthétise
            print(f"🧠 Agent 1 synthétise...")
            synthese = self.agent1_principal(f"Synthétise ces échanges: {reponse1} | {reponse2}")
            resultats["echanges"].append({
                "agent": "agent1_synthese",
                "message": synthese,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            print(f"❌ Erreur dialogue dual: {e}")
            resultats["erreur"] = str(e)
        
        self.dialogue_actif = False
        return resultats
    
    def get_stats_electron(self):
        """Statistiques pour Electron"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "electron_ready": self.electron_ready,
            "dialogue_actif": self.dialogue_actif,
            "agents": {}
        }
        
        for agent_id, agent_info in self.agents.items():
            stats["agents"][agent_id] = {
                "name": agent_info["name"],
                "status": agent_info["status"],
                "port": agent_info["port"],
                "turbo_factor": agent_info["turbo_factor"]
            }
        
        return stats
    
    def demarrer_surveillance_ultra_renforcee(self):
        """🚀 SURVEILLANCE ULTRA RENFORCÉE POUR ELECTRON - JEAN-LUC PASSAVE"""
        def surveillance_ultra_worker():
            consecutive_failures = 0
            last_health_check = {}

            while True:
                try:
                    # 🚀 VÉRIFICATION ULTRA RENFORCÉE
                    agents_status = {}

                    for agent_id, agent_info in self.agents.items():
                        # Test de santé approfondi
                        health_status = self.test_agent_health_ultra(agent_id)
                        agents_status[agent_id] = health_status

                        # Détection de dégradation
                        if agent_id in last_health_check:
                            if health_status["response_time"] > last_health_check[agent_id].get("response_time", 0) * 2:
                                print(f"⚠️ {agent_info['name']} ralentissement détecté")
                                self.optimize_agent_connection(agent_id)

                        last_health_check[agent_id] = health_status

                    # Vérifier si Electron est prêt avec critères renforcés
                    ultra_connected = sum(1 for status in agents_status.values() if status["status"] == "ultra_connected")
                    connected = sum(1 for status in agents_status.values() if status["status"] in ["connected", "ultra_connected"])

                    self.electron_ready = (ultra_connected >= 1 and connected == 2)

                    if self.electron_ready:
                        consecutive_failures = 0
                        print("🚀 ELECTRON ULTRA READY - Dual Agents ultra opérationnels")

                        # 🧠 MISE À JOUR MÉMOIRE THERMIQUE
                        try:
                            from jarvis_thermal_memory_manager import get_thermal_stats
                            thermal_stats = get_thermal_stats()
                            print(f"🧠 Mémoire thermique: {thermal_stats['recent_24h']} interactions/24h")
                        except ImportError:
                            pass
                    else:
                        consecutive_failures += 1
                        print(f"🔄 Attente agents ultra (échecs consécutifs: {consecutive_failures})")

                        # Auto-réparation après 3 échecs consécutifs
                        if consecutive_failures >= 3:
                            print("🔧 Auto-réparation connexions ultra déclenchée")
                            self.auto_repair_connections()
                            consecutive_failures = 0

                    # Surveillance adaptative (plus fréquente si problèmes)
                    sleep_time = 15 if consecutive_failures > 0 else 30
                    time.sleep(sleep_time)

                except Exception as e:
                    print(f"❌ Erreur surveillance ultra: {e}")
                    consecutive_failures += 1
                    time.sleep(60)

        surveillance_thread = threading.Thread(target=surveillance_ultra_worker)
        surveillance_thread.daemon = True
        surveillance_thread.start()
        print("🔍 Surveillance Ultra Renforcée démarrée")

        return surveillance_thread

    def test_agent_health_ultra(self, agent_id):
        """Test de santé ultra approfondi pour un agent"""
        agent = self.agents[agent_id]
        start_time = time.time()

        try:
            # Test simple de santé
            response = requests.get(f"http://localhost:{agent['port']}/health", timeout=5)
            response_time = time.time() - start_time

            if response.status_code == 200:
                return {
                    "status": "ultra_connected",
                    "response_time": response_time,
                    "port": agent["port"],
                    "health": "excellent"
                }
            else:
                return {
                    "status": "degraded",
                    "response_time": response_time,
                    "port": agent["port"],
                    "health": "poor"
                }
        except:
            return {
                "status": "disconnected",
                "response_time": 999,
                "port": agent["port"],
                "health": "critical"
            }

    def optimize_agent_connection(self, agent_id):
        """Optimise la connexion d'un agent spécifique"""
        print(f"🔧 Optimisation connexion {agent_id}")
        # Réinitialiser le statut pour forcer une reconnexion
        self.agents[agent_id]["status"] = "optimizing"

    def auto_repair_connections(self):
        """Auto-réparation des connexions"""
        print("🔧 AUTO-RÉPARATION CONNEXIONS ULTRA")
        for agent_id in self.agents:
            self.agents[agent_id]["status"] = "repairing"
            print(f"🔧 Réparation {agent_id}")
        time.sleep(5)  # Laisser le temps aux agents de se stabiliser

# Instance globale pour Electron
dual_agents_electron = JarvisDualAgentsElectron()

def demarrer_dual_agents_electron():
    """🚀 DÉMARRE LE SYSTÈME DUAL AGENTS ULTRA RENFORCÉ POUR ELECTRON"""
    dual_agents_electron.demarrer_surveillance_ultra_renforcee()
    return dual_agents_electron

def agent1_principal(message):
    """Interface Agent 1 pour Electron"""
    return dual_agents_electron.agent1_principal(message)

def agent2_turbo_accelerateur(message, mode="turbo"):
    """Interface Agent 2 pour Electron"""
    return dual_agents_electron.agent2_turbo_accelerateur(message, mode)

def dialogue_dual_agents(sujet):
    """Interface dialogue dual pour Electron"""
    return dual_agents_electron.dialogue_dual_agents(sujet)

def creer_reve_dual_agents(contexte="créativité"):
    """Interface création de rêves pour Electron"""
    return dual_agents_electron.creer_reve_avec_agents(contexte)

def generer_image_dual_agents(prompt, style="realistic", agent_id="agent2"):
    """🎨 Interface génération d'images 100% LOCALE avec dual agents"""
    try:
        from jarvis_generateur_multimedia_complet import GenerateurMultimediaJARVIS

        # Optimiser le prompt avec l'agent
        prompt_optimise = dual_agents_electron.agent2_turbo_accelerateur(prompt, "generation_image")

        # Générer l'image avec modèle LOCAL SEULEMENT
        generateur = GenerateurMultimediaJARVIS()
        # Forcer l'utilisation du modèle local (pas d'API)
        model_local = "stable_diffusion" if style in ["realistic", "artistic"] else "local_diffusers"
        result = generateur.generer_image(prompt_optimise, style, "1024x1024", model_local)

        return {
            "type": "image_dual_agents",
            "prompt_original": prompt,
            "prompt_optimise": prompt_optimise,
            "result": result,
            "agent": agent_id,
            "model": f"{model_local} (100% Local)"
        }
    except Exception as e:
        return {"type": "erreur", "message": str(e)}

def generer_video_dual_agents(prompt, duree=5, agent_id="agent2"):
    """🎬 Interface génération de vidéos 100% LOCALE avec dual agents"""
    try:
        from jarvis_generateur_multimedia_complet import GenerateurMultimediaJARVIS

        # Optimiser le prompt avec l'agent
        prompt_optimise = dual_agents_electron.agent2_turbo_accelerateur(prompt, "generation_video")

        # Générer la vidéo avec modèle LOCAL SEULEMENT
        generateur = GenerateurMultimediaJARVIS()
        # Forcer l'utilisation du modèle local FFmpeg (pas d'API)
        result = generateur.generer_video(prompt_optimise, duree, 24, "1280x720", "local_ffmpeg")

        return {
            "type": "video_dual_agents",
            "prompt_original": prompt,
            "prompt_optimise": prompt_optimise,
            "result": result,
            "agent": agent_id,
            "model": "FFmpeg Local (100% Local)"
        }
    except Exception as e:
        return {"type": "erreur", "message": str(e)}

def get_stats_electron():
    """Interface stats pour Electron"""
    return dual_agents_electron.get_stats_electron()

def is_electron_ready():
    """Vérifie si Electron est prêt"""
    return dual_agents_electron.electron_ready

if __name__ == "__main__":
    print("🚀 JARVIS DUAL AGENTS DEEPSEEK R1 8B POUR ELECTRON")
    print("=" * 60)
    print("🤖 Agent 1: Principal (Port 8000)")
    print("⚡ Agent 2: Turbo Accélérateur (Port 8001)")
    print("🖥️ Application: Electron")
    print("=" * 60)
    
    # Démarrer le système
    system = demarrer_dual_agents_electron()
    
    # Test des agents
    print("\n🧪 TEST DES DUAL AGENTS:")
    
    test_message = "Bonjour JARVIS, teste les dual agents"
    
    print(f"\n🤖 Agent 1: {agent1_principal(test_message)}")
    print(f"\n⚡ Agent 2: {agent2_turbo_accelerateur('Hello JARVIS turbo', 'traduction')}")
    
    # Test dialogue dual
    print(f"\n🔄 Dialogue Dual:")
    dialogue = dialogue_dual_agents("Intelligence artificielle et créativité")
    print(f"📊 Résultat: {len(dialogue.get('echanges', []))} échanges")
    
    # Stats Electron
    stats = get_stats_electron()
    print(f"\n📊 Stats Electron: Ready={stats['electron_ready']}")
    
    try:
        while True:
            time.sleep(10)
            if is_electron_ready():
                print("🚀 ELECTRON READY - Dual Agents opérationnels")
            else:
                print("🔄 Attente connexion dual agents...")
    except KeyboardInterrupt:
        print("\n🚀 Dual Agents Electron arrêtés")
