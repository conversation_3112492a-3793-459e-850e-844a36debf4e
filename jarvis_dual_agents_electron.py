#!/usr/bin/env python3
"""
🚀 JARVIS DUAL AGENTS DEEPSEEK R1 8B POUR ELECTRON
Agent 1 + Agent 2 avec Turbo Accélérateurs Ultra
Créé pour Jean-Luc <PERSON>ave - Application Electron
"""

import requests
import threading
import time
import json
from datetime import datetime

class JarvisDualAgentsElectron:
    """Gestionnaire dual agents DeepSeek R1 8B pour Electron"""
    
    def __init__(self):
        self.agents = {
            "agent1": {
                "name": "Agent 1 Principal",
                "port": 8000,
                "endpoint": "http://localhost:8000/v1/chat/completions",
                "model": "deepseek-r1",
                "status": "disconnected",
                "role": "Agent principal JARVIS - Dialogue et réflexion",
                "turbo_factor": 1.0
            },
            "agent2": {
                "name": "Agent 2 Turbo Accélérateur",
                "port": 8001,
                "endpoint": "http://localhost:8001/v1/chat/completions",
                "model": "deepseek-r1",
                "status": "disconnected", 
                "role": "Agent turbo accélérateur - Traduction et optimisation",
                "turbo_factor": 2.0
            }
        }
        
        self.electron_ready = False
        self.dialogue_actif = False
        
        print("🚀 DUAL AGENTS DEEPSEEK R1 8B pour ELECTRON initialisés")
        
    def verifier_agents(self):
        """Vérifie le statut des deux agents"""
        for agent_id, agent_info in self.agents.items():
            try:
                # Test de connexion simple
                response = requests.get(f"http://localhost:{agent_info['port']}/health", timeout=3)
                if response.status_code == 200:
                    self.agents[agent_id]["status"] = "connected"
                    print(f"✅ {agent_info['name']} connecté sur port {agent_info['port']}")
                else:
                    self.agents[agent_id]["status"] = "error"
            except:
                self.agents[agent_id]["status"] = "disconnected"
                print(f"❌ {agent_info['name']} déconnecté sur port {agent_info['port']}")
    
    def envoyer_a_agent(self, agent_id, message, temperature=0.7, max_tokens=200):
        """Envoie un message à un agent spécifique"""
        if agent_id not in self.agents:
            return f"❌ Agent {agent_id} inconnu"
        
        agent = self.agents[agent_id]
        
        try:
            payload = {
                "model": agent["model"],
                "messages": [
                    {
                        "role": "system",
                        "content": f"Tu es {agent['role']} de JARVIS dans une application Electron. Réponds de manière concise et utile en français."
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                "stream": False,
                "temperature": temperature * agent["turbo_factor"],
                "max_tokens": max_tokens
            }
            
            response = requests.post(agent["endpoint"], json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                reponse = result["choices"][0]["message"]["content"]
                self.agents[agent_id]["status"] = "connected"
                print(f"✅ {agent['name']} répond: {reponse[:50]}...")
                return reponse
            else:
                self.agents[agent_id]["status"] = "error"
                return f"❌ Erreur {agent['name']}: {response.status_code}"
                
        except Exception as e:
            self.agents[agent_id]["status"] = "disconnected"
            print(f"❌ Erreur connexion {agent['name']}: {e}")
            return f"🔄 {agent['name']} en reconnexion..."
    
    def agent1_principal(self, message):
        """Agent 1 - Principal JARVIS"""
        return self.envoyer_a_agent("agent1", message, 0.7, 300)
    
    def agent2_turbo_accelerateur(self, message, mode="turbo"):
        """Agent 2 - Turbo Accélérateur"""
        if mode == "traduction":
            prompt = f"Traduis et optimise en français: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.3, 200)
        elif mode == "acceleration":
            prompt = f"Accélère et optimise cette réponse: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.5, 250)
        else:
            prompt = f"Mode turbo - Traite rapidement: {message}"
            return self.envoyer_a_agent("agent2", prompt, 0.8, 200)
    
    def dialogue_dual_agents(self, sujet):
        """Dialogue entre Agent 1 et Agent 2"""
        self.dialogue_actif = True
        
        resultats = {
            "sujet": sujet,
            "timestamp": datetime.now().isoformat(),
            "echanges": []
        }
        
        try:
            # Agent 1 initie
            print(f"🤖 Agent 1 initie sur: {sujet}")
            reponse1 = self.agent1_principal(f"Initie une réflexion sur: {sujet}")
            resultats["echanges"].append({
                "agent": "agent1",
                "message": reponse1,
                "timestamp": datetime.now().isoformat()
            })
            
            # Agent 2 accélère et optimise
            print(f"⚡ Agent 2 turbo accélère...")
            reponse2 = self.agent2_turbo_accelerateur(f"Accélère cette réflexion: {reponse1}", "acceleration")
            resultats["echanges"].append({
                "agent": "agent2",
                "message": reponse2,
                "timestamp": datetime.now().isoformat()
            })
            
            # Agent 1 synthétise
            print(f"🧠 Agent 1 synthétise...")
            synthese = self.agent1_principal(f"Synthétise ces échanges: {reponse1} | {reponse2}")
            resultats["echanges"].append({
                "agent": "agent1_synthese",
                "message": synthese,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            print(f"❌ Erreur dialogue dual: {e}")
            resultats["erreur"] = str(e)
        
        self.dialogue_actif = False
        return resultats
    
    def get_stats_electron(self):
        """Statistiques pour Electron"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "electron_ready": self.electron_ready,
            "dialogue_actif": self.dialogue_actif,
            "agents": {}
        }
        
        for agent_id, agent_info in self.agents.items():
            stats["agents"][agent_id] = {
                "name": agent_info["name"],
                "status": agent_info["status"],
                "port": agent_info["port"],
                "turbo_factor": agent_info["turbo_factor"]
            }
        
        return stats
    
    def demarrer_surveillance_electron(self):
        """Démarre la surveillance pour Electron"""
        def surveillance_worker():
            while True:
                try:
                    self.verifier_agents()
                    
                    # Vérifier si Electron est prêt
                    agents_connectes = sum(1 for a in self.agents.values() if a["status"] == "connected")
                    self.electron_ready = (agents_connectes == 2)
                    
                    if self.electron_ready:
                        print("🚀 ELECTRON READY - Dual Agents opérationnels")
                    
                    time.sleep(30)  # Vérification toutes les 30 secondes
                except Exception as e:
                    print(f"❌ Erreur surveillance Electron: {e}")
                    time.sleep(60)
        
        surveillance_thread = threading.Thread(target=surveillance_worker)
        surveillance_thread.daemon = True
        surveillance_thread.start()
        print("🔍 Surveillance Electron démarrée")
        
        return surveillance_thread

# Instance globale pour Electron
dual_agents_electron = JarvisDualAgentsElectron()

def demarrer_dual_agents_electron():
    """Démarre le système dual agents pour Electron"""
    dual_agents_electron.demarrer_surveillance_electron()
    return dual_agents_electron

def agent1_principal(message):
    """Interface Agent 1 pour Electron"""
    return dual_agents_electron.agent1_principal(message)

def agent2_turbo_accelerateur(message, mode="turbo"):
    """Interface Agent 2 pour Electron"""
    return dual_agents_electron.agent2_turbo_accelerateur(message, mode)

def dialogue_dual_agents(sujet):
    """Interface dialogue dual pour Electron"""
    return dual_agents_electron.dialogue_dual_agents(sujet)

def get_stats_electron():
    """Interface stats pour Electron"""
    return dual_agents_electron.get_stats_electron()

def is_electron_ready():
    """Vérifie si Electron est prêt"""
    return dual_agents_electron.electron_ready

if __name__ == "__main__":
    print("🚀 JARVIS DUAL AGENTS DEEPSEEK R1 8B POUR ELECTRON")
    print("=" * 60)
    print("🤖 Agent 1: Principal (Port 8000)")
    print("⚡ Agent 2: Turbo Accélérateur (Port 8001)")
    print("🖥️ Application: Electron")
    print("=" * 60)
    
    # Démarrer le système
    system = demarrer_dual_agents_electron()
    
    # Test des agents
    print("\n🧪 TEST DES DUAL AGENTS:")
    
    test_message = "Bonjour JARVIS, teste les dual agents"
    
    print(f"\n🤖 Agent 1: {agent1_principal(test_message)}")
    print(f"\n⚡ Agent 2: {agent2_turbo_accelerateur('Hello JARVIS turbo', 'traduction')}")
    
    # Test dialogue dual
    print(f"\n🔄 Dialogue Dual:")
    dialogue = dialogue_dual_agents("Intelligence artificielle et créativité")
    print(f"📊 Résultat: {len(dialogue.get('echanges', []))} échanges")
    
    # Stats Electron
    stats = get_stats_electron()
    print(f"\n📊 Stats Electron: Ready={stats['electron_ready']}")
    
    try:
        while True:
            time.sleep(10)
            if is_electron_ready():
                print("🚀 ELECTRON READY - Dual Agents opérationnels")
            else:
                print("🔄 Attente connexion dual agents...")
    except KeyboardInterrupt:
        print("\n🚀 Dual Agents Electron arrêtés")
