#!/usr/bin/env python3
"""
INTERFACE MULTIMÉDIA COMPLÈTE JARVIS - JEAN-LUC PASSAVE
Génération complète: Images, Vidéos, Musique avec IA
"""

import gradio as gr
from jarvis_generateur_multimedia_complet import get_generateur_multimedia

def create_jarvis_chat_component():
    """Composant JARVIS chat réutilisable"""
    with gr.<PERSON>():
        with gr.<PERSON>umn(scale=4):
            jarvis_chat = gr.Chatbot(
                label="💬 JARVIS Assistant",
                height=200,
                show_label=True
            )
        with gr.Column(scale=1):
            home_btn = gr.<PERSON><PERSON>("🏠 Accueil", variant="secondary", scale=1)
    
    with gr.Row():
        jarvis_input = gr.Textbox(
            placeholder="Parlez à JARVIS...",
            label="💬 Message",
            scale=4
        )
        jarvis_send_btn = gr.<PERSON><PERSON>("📤 Envoyer", variant="primary", scale=1)
    
    return jarvis_chat, jarvis_input, jarvis_send_btn, home_btn

def create_multimedia_complete_interface():
    """Interface multimédia complète avec toutes les fonctionnalités"""
    
    with gr.<PERSON><PERSON>(
        title="🎨 JARVIS - Générateur Multimédia Complet",
        theme=gr.themes.Soft()
    ) as multimedia_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #E91E63, #F06292); color: white; padding: 10px; margin: -20px -20px 15px -20px;">
            <h2 style="margin: 0; font-size: 1.4em;">🎨 GÉNÉRATEUR MULTIMÉDIA COMPLET JARVIS</h2>
            <p style="margin: 5px 0; font-size: 0.85em;">Génération IA: Images, Vidéos, Musique - Toutes les possibilités créatives</p>
        </div>
        """)
        
        # ONGLETS POUR CHAQUE TYPE DE GÉNÉRATION
        with gr.Tabs():
            
            # ONGLET GÉNÉRATION D'IMAGES
            with gr.TabItem("🎨 Génération d'Images"):
                gr.HTML("<h3>🎨 Générateur d'Images IA Professionnel</h3>")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        image_prompt = gr.Textbox(
                            placeholder="Décrivez l'image que vous voulez générer en détail...",
                            label="📝 Prompt Image",
                            lines=4
                        )
                        
                        with gr.Row():
                            image_style = gr.Dropdown(
                                choices=["realistic", "artistic", "anime", "cyberpunk", "fantasy", "portrait", "landscape", "abstract"],
                                value="realistic",
                                label="🎭 Style Artistique"
                            )
                            image_resolution = gr.Dropdown(
                                choices=["512x512", "768x768", "1024x1024", "1280x720", "1920x1080", "2048x2048"],
                                value="1024x1024",
                                label="📐 Résolution"
                            )
                        
                        with gr.Row():
                            image_model = gr.Dropdown(
                                choices=["stable_diffusion", "dalle", "midjourney", "local"],
                                value="stable_diffusion",
                                label="🤖 Modèle IA"
                            )
                            image_quality = gr.Dropdown(
                                choices=["draft", "standard", "high", "ultra"],
                                value="high",
                                label="⭐ Qualité"
                            )
                        
                        generate_image_btn = gr.Button("🎨 Générer Image", variant="primary", size="lg", elem_classes=["creativity-btn"])
                    
                    with gr.Column(scale=1):
                        image_output = gr.Image(label="🖼️ Image Générée", height=400)
                        image_status = gr.HTML("""
                        <div style='background: #f3e5f5; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #7b1fa2; margin: 0 0 10px 0;'>📊 STATUT GÉNÉRATION</h4>
                            <p style='margin: 5px 0;'>🎨 Prêt à générer</p>
                            <p style='margin: 5px 0;'>⏱️ Temps estimé: 30-60s</p>
                            <p style='margin: 5px 0;'>🔧 Modèle: Stable Diffusion</p>
                            <p style='margin: 5px 0;'>💾 Format: PNG haute qualité</p>
                        </div>
                        """)
                        
                        with gr.Column():
                            save_image_btn = gr.Button("💾 Sauvegarder", variant="secondary")
                            enhance_image_btn = gr.Button("✨ Améliorer", variant="secondary")
            
            # ONGLET GÉNÉRATION DE VIDÉOS
            with gr.TabItem("🎬 Génération de Vidéos"):
                gr.HTML("<h3>🎬 Générateur de Vidéos IA Avancé</h3>")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        video_prompt = gr.Textbox(
                            placeholder="Décrivez la vidéo que vous voulez générer avec des détails sur l'action, l'ambiance...",
                            label="📝 Prompt Vidéo",
                            lines=4
                        )
                        
                        with gr.Row():
                            video_duration = gr.Slider(
                                minimum=3,
                                maximum=60,
                                value=10,
                                step=1,
                                label="⏱️ Durée (secondes)"
                            )
                            video_fps = gr.Dropdown(
                                choices=[24, 30, 60],
                                value=30,
                                label="🎞️ Images/seconde"
                            )
                        
                        with gr.Row():
                            video_resolution = gr.Dropdown(
                                choices=["720x480", "1280x720", "1920x1080", "2560x1440", "3840x2160"],
                                value="1920x1080",
                                label="📐 Résolution"
                            )
                            video_model = gr.Dropdown(
                                choices=["stable_video", "runway", "pika", "local_ffmpeg"],
                                value="local_ffmpeg",
                                label="🤖 Modèle IA"
                            )
                        
                        with gr.Row():
                            video_style = gr.Dropdown(
                                choices=["cinematic", "documentary", "animation", "artistic", "realistic"],
                                value="cinematic",
                                label="🎭 Style Vidéo"
                            )
                            video_motion = gr.Dropdown(
                                choices=["slow", "normal", "fast", "dynamic"],
                                value="normal",
                                label="🏃 Mouvement"
                            )
                        
                        generate_video_btn = gr.Button("🎬 Générer Vidéo", variant="primary", size="lg", elem_classes=["creativity-btn"])
                    
                    with gr.Column(scale=1):
                        video_output = gr.Video(label="🎬 Vidéo Générée", height=300)
                        video_status = gr.HTML("""
                        <div style='background: #e3f2fd; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #1976d2; margin: 0 0 10px 0;'>📊 STATUT GÉNÉRATION</h4>
                            <p style='margin: 5px 0;'>🎬 Prêt à générer</p>
                            <p style='margin: 5px 0;'>⏱️ Temps estimé: 2-10 min</p>
                            <p style='margin: 5px 0;'>🔧 Modèle: FFmpeg Local</p>
                            <p style='margin: 5px 0;'>💾 Format: MP4 H.264</p>
                        </div>
                        """)
                        
                        video_progress = gr.HTML("""
                        <div style='background: #fff3e0; padding: 10px; border-radius: 5px; margin: 10px 0;'>
                            <p style='margin: 0; font-size: 0.9em;'>📊 Progression: En attente</p>
                        </div>
                        """)
                        
                        with gr.Column():
                            preview_video_btn = gr.Button("👁️ Aperçu", variant="secondary")
                            export_video_btn = gr.Button("📤 Exporter", variant="secondary")
            
            # ONGLET GÉNÉRATION DE MUSIQUE
            with gr.TabItem("🎵 Génération de Musique"):
                gr.HTML("<h3>🎵 Générateur de Musique IA Professionnel</h3>")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        music_prompt = gr.Textbox(
                            placeholder="Décrivez la musique: style, instruments, ambiance, tempo...",
                            label="📝 Prompt Musical",
                            lines=4
                        )
                        
                        with gr.Row():
                            music_duration = gr.Slider(
                                minimum=10,
                                maximum=300,
                                value=60,
                                step=5,
                                label="⏱️ Durée (secondes)"
                            )
                            music_tempo = gr.Slider(
                                minimum=60,
                                maximum=180,
                                value=120,
                                step=5,
                                label="🥁 Tempo (BPM)"
                            )
                        
                        with gr.Row():
                            music_style = gr.Dropdown(
                                choices=["electronic", "classical", "rock", "jazz", "ambient", "cinematic", "pop", "hip-hop", "folk"],
                                value="electronic",
                                label="🎭 Style Musical"
                            )
                            music_mood = gr.Dropdown(
                                choices=["happy", "sad", "energetic", "calm", "mysterious", "epic", "romantic"],
                                value="calm",
                                label="😊 Ambiance"
                            )
                        
                        with gr.Row():
                            music_model = gr.Dropdown(
                                choices=["musicgen", "suno", "audiocraft", "local_synthesis"],
                                value="local_synthesis",
                                label="🤖 Modèle IA"
                            )
                            music_quality = gr.Dropdown(
                                choices=["draft", "standard", "high", "studio"],
                                value="high",
                                label="🎧 Qualité Audio"
                            )
                        
                        generate_music_btn = gr.Button("🎵 Générer Musique", variant="primary", size="lg", elem_classes=["creativity-btn"])
                    
                    with gr.Column(scale=1):
                        music_output = gr.Audio(label="🎵 Musique Générée")
                        music_status = gr.HTML("""
                        <div style='background: #fff3e0; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #f57c00; margin: 0 0 10px 0;'>📊 STATUT GÉNÉRATION</h4>
                            <p style='margin: 5px 0;'>🎵 Prêt à générer</p>
                            <p style='margin: 5px 0;'>⏱️ Temps estimé: 1-5 min</p>
                            <p style='margin: 5px 0;'>🔧 Modèle: Synthèse Locale</p>
                            <p style='margin: 5px 0;'>💾 Format: WAV 44.1kHz</p>
                        </div>
                        """)
                        
                        music_waveform = gr.HTML("""
                        <div style='background: #f5f5f5; padding: 15px; border-radius: 10px; text-align: center;'>
                            <p style='margin: 0; color: #666;'>🌊 Forme d'onde apparaîtra ici</p>
                        </div>
                        """)
                        
                        with gr.Column():
                            loop_music_btn = gr.Button("🔄 Boucle", variant="secondary")
                            remix_music_btn = gr.Button("🎛️ Remix", variant="secondary")
            
            # ONGLET HISTORIQUE ET GESTION
            with gr.TabItem("📊 Historique & Gestion"):
                gr.HTML("<h3>📊 Gestion Complète des Créations</h3>")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        generation_history = gr.HTML("""
                        <div style='background: white; padding: 15px; border-radius: 10px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd;'>
                            <h4 style='color: #333; margin: 0 0 10px 0;'>📋 Historique des Créations</h4>
                            <div style='margin: 10px 0; padding: 10px; background: #f3e5f5; border-radius: 5px; border-left: 3px solid #9C27B0;'>
                                <strong>🎨 Image:</strong> Portrait cyberpunk futuriste<br>
                                <em>Créé:</em> Il y a 5 minutes - <em>Taille:</em> 2.1 MB
                            </div>
                            <div style='margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 5px; border-left: 3px solid #2196F3;'>
                                <strong>🎬 Vidéo:</strong> Animation paysage montagne<br>
                                <em>Créé:</em> Il y a 15 minutes - <em>Taille:</em> 45.7 MB
                            </div>
                            <div style='margin: 10px 0; padding: 10px; background: #fff3e0; border-radius: 5px; border-left: 3px solid #FF9800;'>
                                <strong>🎵 Musique:</strong> Ambient électronique<br>
                                <em>Créé:</em> Il y a 30 minutes - <em>Taille:</em> 8.3 MB
                            </div>
                        </div>
                        """)
                        
                        with gr.Row():
                            refresh_history_btn = gr.Button("🔄 Actualiser", variant="secondary")
                            filter_history_btn = gr.Button("🔍 Filtrer", variant="secondary")
                            clear_history_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    
                    with gr.Column(scale=1):
                        multimedia_stats = gr.HTML("""
                        <div style='background: #f5f5f5; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #333; margin: 0 0 10px 0;'>📈 Statistiques Globales</h4>
                            <p style='margin: 5px 0;'>🎨 Images générées: 0</p>
                            <p style='margin: 5px 0;'>🎬 Vidéos créées: 0</p>
                            <p style='margin: 5px 0;'>🎵 Musiques composées: 0</p>
                            <p style='margin: 5px 0;'>💾 Espace utilisé: 0 MB</p>
                            <p style='margin: 5px 0;'>⚡ Modèles actifs: 12</p>
                            <p style='margin: 5px 0;'>🚀 Temps moyen: 2.3 min</p>
                        </div>
                        """)
                        
                        performance_stats = gr.HTML("""
                        <div style='background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 15px 0;'>
                            <h4 style='color: #2e7d32; margin: 0 0 10px 0;'>⚡ Performances</h4>
                            <p style='margin: 5px 0;'>🔥 GPU: Disponible</p>
                            <p style='margin: 5px 0;'>💾 RAM: 67% utilisée</p>
                            <p style='margin: 5px 0;'>⚡ Vitesse: Optimale</p>
                        </div>
                        """)
                        
                        with gr.Column():
                            export_all_btn = gr.Button("📦 Exporter Tout", variant="primary")
                            optimize_storage_btn = gr.Button("🗜️ Optimiser", variant="secondary")
                            backup_creations_btn = gr.Button("💾 Sauvegarder", variant="secondary")
        
        # FONCTIONS DE GÉNÉRATION AVEC DUAL AGENTS
        def generate_image_function(prompt, style, resolution, model, quality):
            """🎨 Génération d'images avec optimisation par dual agents"""
            try:
                # 🚀 UTILISER DUAL AGENTS POUR OPTIMISER LE PROMPT
                from jarvis_dual_agents_electron import generer_image_dual_agents, is_electron_ready

                if is_electron_ready():
                    result = generer_image_dual_agents(prompt, style)

                    if result and result.get("type") == "image_dual_agents":
                        status_html = f"""
                        <div style='background: #e8f5e8; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #2e7d32; margin: 0 0 10px 0;'>✅ GÉNÉRATION DUAL AGENTS RÉUSSIE</h4>
                            <p style='margin: 5px 0;'>🎨 Image créée avec Agent 2 Turbo</p>
                            <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                            <p style='margin: 5px 0;'>🎭 Style: {style}</p>
                            <p style='margin: 5px 0;'>🤖 Modèle: {model} + DeepSeek R1 8B</p>
                            <p style='margin: 5px 0;'>⚡ Prompt optimisé par Agent Turbo</p>
                        </div>
                        """
                        return result['result']['filepath'] if result['result'] else None, status_html
                    else:
                        # Fallback vers générateur classique
                        generateur = get_generateur_multimedia()
                        result = generateur.generer_image(prompt, style, resolution, model)

                        if result:
                            status_html = f"""
                            <div style='background: #fff3e0; padding: 15px; border-radius: 10px;'>
                                <h4 style='color: #f57c00; margin: 0 0 10px 0;'>✅ GÉNÉRATION CLASSIQUE</h4>
                                <p style='margin: 5px 0;'>🎨 Image créée: {result['filename']}</p>
                                <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                                <p style='margin: 5px 0;'>🎭 Style: {style}</p>
                                <p style='margin: 5px 0;'>🤖 Modèle: {model}</p>
                                <p style='margin: 5px 0;'>⚠️ Dual Agents non disponibles</p>
                            </div>
                            """
                            return result['filepath'], status_html
                        else:
                            error_html = """
                            <div style='background: #ffebee; padding: 15px; border-radius: 10px;'>
                                <h4 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR GÉNÉRATION</h4>
                                <p style='margin: 5px 0;'>Vérifiez les paramètres et réessayez</p>
                            </div>
                            """
                            return None, error_html
                else:
                    # Dual agents non prêts, utiliser générateur classique
                    generateur = get_generateur_multimedia()
                    result = generateur.generer_image(prompt, style, resolution, model)

                    if result:
                        status_html = f"""
                        <div style='background: #fff3e0; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #f57c00; margin: 0 0 10px 0;'>✅ GÉNÉRATION CLASSIQUE</h4>
                            <p style='margin: 5px 0;'>🎨 Image créée: {result['filename']}</p>
                            <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                            <p style='margin: 5px 0;'>🎭 Style: {style}</p>
                            <p style='margin: 5px 0;'>🤖 Modèle: {model}</p>
                            <p style='margin: 5px 0;'>🔄 Dual Agents en initialisation</p>
                        </div>
                        """
                        return result['filepath'], status_html
                    else:
                        error_html = """
                        <div style='background: #ffebee; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR GÉNÉRATION</h4>
                            <p style='margin: 5px 0;'>Vérifiez les paramètres et réessayez</p>
                        </div>
                        """
                        return None, error_html

            except Exception as e:
                error_html = f"""
                <div style='background: #ffebee; padding: 15px; border-radius: 10px;'>
                    <h4 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR SYSTÈME</h4>
                    <p style='margin: 5px 0;'>Erreur: {str(e)}</p>
                </div>
                """
                return None, error_html
        
        def generate_video_function(prompt, duration, fps, resolution, model, style, motion):
            """🎬 Génération de vidéos avec optimisation par dual agents"""
            try:
                # 🚀 UTILISER DUAL AGENTS POUR OPTIMISER LE PROMPT VIDÉO
                from jarvis_dual_agents_electron import generer_video_dual_agents, is_electron_ready

                if is_electron_ready():
                    result = generer_video_dual_agents(prompt, duration)

                    if result and result.get("type") == "video_dual_agents":
                        status_html = f"""
                        <div style='background: #e8f5e8; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #2e7d32; margin: 0 0 10px 0;'>✅ GÉNÉRATION VIDÉO DUAL AGENTS</h4>
                            <p style='margin: 5px 0;'>🎬 Vidéo créée avec Agent 2 Turbo</p>
                            <p style='margin: 5px 0;'>⏱️ Durée: {duration}s</p>
                            <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                            <p style='margin: 5px 0;'>🎞️ FPS: {fps}</p>
                            <p style='margin: 5px 0;'>🤖 Modèle: {model} + DeepSeek R1 8B</p>
                            <p style='margin: 5px 0;'>⚡ Prompt optimisé par Agent Turbo</p>
                        </div>
                        """

                        progress_html = """
                        <div style='background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0;'>
                            <p style='margin: 0; font-size: 0.9em;'>📊 Progression: ✅ Terminé avec Dual Agents</p>
                        </div>
                        """

                        return result['result']['filepath'] if result['result'] else None, status_html, progress_html
                    else:
                        # Fallback vers générateur classique
                        generateur = get_generateur_multimedia()
                        result = generateur.generer_video(prompt, duration, fps, resolution, model)

                        status_html = f"""
                        <div style='background: #fff3e0; padding: 15px; border-radius: 10px;'>
                            <h4 style='color: #f57c00; margin: 0 0 10px 0;'>🎬 GÉNÉRATION CLASSIQUE</h4>
                            <p style='margin: 5px 0;'>⏱️ Durée: {duration}s</p>
                            <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                            <p style='margin: 5px 0;'>🎞️ FPS: {fps}</p>
                            <p style='margin: 5px 0;'>🤖 Modèle: {model}</p>
                            <p style='margin: 5px 0;'>⚠️ Dual Agents non disponibles</p>
                        </div>
                        """

                        progress_html = """
                        <div style='background: #fff3e0; padding: 10px; border-radius: 5px; margin: 10px 0;'>
                            <p style='margin: 0; font-size: 0.9em;'>📊 Progression: Génération classique en cours...</p>
                        </div>
                        """

                        return None, status_html, progress_html
                else:
                    # Dual agents non prêts
                    generateur = get_generateur_multimedia()
                    result = generateur.generer_video(prompt, duration, fps, resolution, model)

                    status_html = f"""
                    <div style='background: #fff3e0; padding: 15px; border-radius: 10px;'>
                        <h4 style='color: #f57c00; margin: 0 0 10px 0;'>🎬 GÉNÉRATION CLASSIQUE</h4>
                        <p style='margin: 5px 0;'>⏱️ Durée: {duration}s</p>
                        <p style='margin: 5px 0;'>📐 Résolution: {resolution}</p>
                        <p style='margin: 5px 0;'>🎞️ FPS: {fps}</p>
                        <p style='margin: 5px 0;'>🤖 Modèle: {model}</p>
                        <p style='margin: 5px 0;'>🔄 Dual Agents en initialisation</p>
                    </div>
                    """

                    progress_html = """
                    <div style='background: #fff3e0; padding: 10px; border-radius: 5px; margin: 10px 0;'>
                        <p style='margin: 0; font-size: 0.9em;'>📊 Progression: Génération en cours...</p>
                    </div>
                    """

                    return None, status_html, progress_html

            except Exception as e:
                error_html = f"""
                <div style='background: #ffebee; padding: 15px; border-radius: 10px;'>
                    <h4 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR GÉNÉRATION VIDÉO</h4>
                    <p style='margin: 5px 0;'>Erreur: {str(e)}</p>
                </div>
                """

                error_progress = """
                <div style='background: #ffebee; padding: 10px; border-radius: 5px; margin: 10px 0;'>
                    <p style='margin: 0; font-size: 0.9em;'>📊 Progression: ❌ Erreur</p>
                </div>
                """

                return None, error_html, error_progress
        
        def generate_music_function(prompt, duration, tempo, style, mood, model, quality):
            generateur = get_generateur_multimedia()
            result = generateur.generer_musique(prompt, duration, style, model)
            
            if result:
                status_html = f"""
                <div style='background: #e8f5e8; padding: 15px; border-radius: 10px;'>
                    <h4 style='color: #2e7d32; margin: 0 0 10px 0;'>✅ MUSIQUE GÉNÉRÉE</h4>
                    <p style='margin: 5px 0;'>🎵 Fichier: {result['filename']}</p>
                    <p style='margin: 5px 0;'>⏱️ Durée: {duration}s</p>
                    <p style='margin: 5px 0;'>🎭 Style: {style}</p>
                    <p style='margin: 5px 0;'>🥁 Tempo: {tempo} BPM</p>
                </div>
                """
                
                waveform_html = """
                <div style='background: #f5f5f5; padding: 15px; border-radius: 10px; text-align: center;'>
                    <p style='margin: 0; color: #333;'>🌊 Forme d'onde générée</p>
                </div>
                """
                
                return result['filepath'], status_html, waveform_html
            else:
                error_html = """
                <div style='background: #ffebee; padding: 15px; border-radius: 10px;'>
                    <h4 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR GÉNÉRATION</h4>
                    <p style='margin: 5px 0;'>Problème lors de la génération musicale</p>
                </div>
                """
                return None, error_html, ""
        
        # CONNEXIONS DES BOUTONS
        generate_image_btn.click(
            fn=generate_image_function,
            inputs=[image_prompt, image_style, image_resolution, image_model, image_quality],
            outputs=[image_output, image_status]
        )
        
        generate_video_btn.click(
            fn=generate_video_function,
            inputs=[video_prompt, video_duration, video_fps, video_resolution, video_model, video_style, video_motion],
            outputs=[video_output, video_status, video_progress]
        )
        
        generate_music_btn.click(
            fn=generate_music_function,
            inputs=[music_prompt, music_duration, music_tempo, music_style, music_mood, music_model, music_quality],
            outputs=[music_output, music_status, music_waveform]
        )
        
        # Intégrer JARVIS
        gr.HTML("<hr style='margin: 20px 0;'>")
        jarvis_chat, jarvis_input, jarvis_send_btn, home_btn = create_jarvis_chat_component()
        
        # Connexion bouton retour
        home_btn.click(
            fn=lambda: "🏠 Retour au dashboard principal",
            outputs=[]
        )
    
    return multimedia_interface

if __name__ == "__main__":
    interface = create_multimedia_complete_interface()
    interface.launch(server_port=7881, share=False)
